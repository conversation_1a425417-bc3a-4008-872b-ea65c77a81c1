@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ComplexMethod")

package com.superhexa.supervision.feature.miwear.speechhub.compont

import android.annotation.SuppressLint
import android.content.Context
import android.view.ViewGroup
import android.webkit.WebView
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintLayoutScope
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.github.fragivity.pop
import com.superhexa.supervision.component.AnimatedSheetBg
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.OggDecodeHelper
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.constant.AudioTranscriptionShareType
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.constant.LoadingType
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.helper.ImageShare
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.helper.RecordShare
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.AudioTranscriptionEffect
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.AudioTranscriptionEvent
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.AudioTranscriptionViewModel
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.AudioTranscriptionViewModel.RecordOptionResult
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.AudioTranscriptionViewModel.RecordSummaryFail
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.AudioTranscriptionViewModel.Tab
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.DialogState
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.SpeakerTextDefaults
import com.superhexa.supervision.feature.miwear.speechhub.presentation.router.PageRouter
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.network.NetworkMonitor
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack50
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_123
import com.superhexa.supervision.library.base.basecommon.theme.Dp_13
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_150
import com.superhexa.supervision.library.base.basecommon.theme.Dp_155
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_21
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Dp_25
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.theme.Dp_34
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_46
import com.superhexa.supervision.library.base.basecommon.theme.Dp_469
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_54
import com.superhexa.supervision.library.base.basecommon.theme.Dp_58
import com.superhexa.supervision.library.base.basecommon.theme.Dp_6
import com.superhexa.supervision.library.base.basecommon.theme.Dp_70
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_80
import com.superhexa.supervision.library.base.basecommon.theme.Dp_9
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_14
import com.superhexa.supervision.library.base.basecommon.theme.Sp_15
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_21
import com.superhexa.supervision.library.base.basecommon.theme.Sp_28
import com.superhexa.supervision.library.base.basecommon.tools.IntentUtils
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.webviewhelper.StreamType
import com.superhexa.supervision.library.base.webviewhelper.WebAppInterfaceListener
import com.superhexa.supervision.library.base.webviewhelper.WebPageHelper
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.ai.capability.constant.Language
import com.xiaomi.ai.capability.request.model.Phrase
import com.xiaomi.ai.capability.request.model.Sentence
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

private const val SPEAKER_NAME_MAX = 20
private const val DISTINGUISH_SPEAKERS = 100L

@Composable
fun AudioTranscriptionScreen(viewModel: AudioTranscriptionViewModel, navigator: NavController) {
    val effect by viewModel.effect.collectAsState(initial = null)
    val dialogState by viewModel.dialogState.collectAsState()
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val recordResult = viewModel.recordResultLiveData.observeAsState()
    val transOrSummaryFail by viewModel.transcribeOrSummaryFail.collectAsState()

    LaunchedEffect(effect) {
        handleEffect(viewModel, effect, context, scope, navigator)
    }
    ConstraintLayout(
        modifier = Modifier
            .fillMaxSize()
            .background(color = ColorBlack)
    ) {
        val (
            title, summaryTips, webContent, list,
            emptyView, progressView, bottomBtn, failRef
        ) = createRefs()

        ActionTitleBar(
            viewModel,
            modifier = Modifier.constrainAs(title) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            onClick = { navigator.pop() },
            onShareClick = {
                viewModel.showShareMenu(true)
                O95Statistic.clickTranscribeAndSummaryOption(
                    name = clickBackName(viewModel.tabLiveData.value),
                    positionName = "share_button"
                )
            },
            onMoreClick = {
                viewModel.showMenu(true)
                O95Statistic.clickTranscribeAndSummaryOption(
                    name = clickBackName(viewModel.tabLiveData.value),
                    positionName = "more_button"
                )
            }
        )

        val tab = viewModel.tabLiveData.value
        val recordOptionResult = recordResult.value
        Timber.d(
            "AudioTranscriptionScreen ActionTitleBar tab:$tab," +
                " recordResult:$recordOptionResult, fail: $transOrSummaryFail"
        )
        when (recordOptionResult) {
            is RecordOptionResult.UploadingOption -> {
                when (recordOptionResult.status) {
                    AudioTranscriptionViewModel.UploadingStatus.UPLOADING -> {
                        TranscriptionInProgress(
                            progressView,
                            topRef = title,
                            bottomRef = bottomBtn,
                            loadingType = 0
                        )
                    }

                    AudioTranscriptionViewModel.UploadingStatus.FAILED -> {
                        TransliterateFail(
                            viewModel,
                            transOrSummaryFail,
                            topRef = title,
                            bottomRef = bottomBtn,
                            transcriptionFail = failRef,
                            uploadFailed = true
                        )
                    }

                    // 没有上传成功的UI中间态，暂时不做
                    else -> Unit
                }
            }

            is RecordOptionResult.LoadingOption -> {
                val transcribeLoading = recordOptionResult.transcribeLoading
                val summaryLoading = recordOptionResult.summaryLoading
                if (tab == Tab.Transcribe) {
                    if (transcribeLoading) {
                        if (transOrSummaryFail is RecordSummaryFail.TranslateFail) {
                            TransliterateFail(
                                viewModel,
                                transOrSummaryFail,
                                topRef = title,
                                bottomRef = bottomBtn,
                                transcriptionFail = failRef
                            )
                        } else {
                            TranscriptionInProgress(
                                progressView,
                                topRef = title,
                                bottomRef = bottomBtn,
                                loadingType = 1
                            )
                        }
                    } else {
                        ShowTranscription(
                            viewModel,
                            title = title,
                            list = list,
                            emptyView = emptyView,
                            bottomBtn = bottomBtn,
                            failRef = failRef
                        )
                    }
                } else {
                    if (summaryLoading) {
                        if (transOrSummaryFail is RecordSummaryFail.SummaryFail) {
                            TransliterateFail(
                                viewModel,
                                transOrSummaryFail,
                                topRef = title,
                                bottomRef = bottomBtn,
                                transcriptionFail = failRef
                            )
                        } else {
                            TranscriptionInProgress(
                                progressView,
                                topRef = title,
                                bottomRef = bottomBtn,
                                loadingType = 2
                            )
                        }
                    } else {
                        // 转写生成中
                        ShowSummary(
                            viewModel = viewModel,
                            titleRef = title,
                            webContent = webContent,
                            summaryTips = summaryTips,
                            bottomBtn = bottomBtn,
                            progressView = progressView,
                            failRef = failRef
                        )
                    }
                }
            }

            is RecordOptionResult.TranscribeOption -> {
                if (tab == Tab.Transcribe) {
                    TabTranscription(
                        viewModel,
                        transOrSummaryFail,
                        title,
                        list,
                        progressView,
                        emptyView,
                        bottomBtn,
                        failRef
                    )
                } else if (tab == Tab.Summary) {
                    TabSummary(
                        viewModel = viewModel,
                        transOrSummaryFail = transOrSummaryFail,
                        title = title,
                        progressView = progressView,
                        webContent = webContent,
                        summaryTips = summaryTips,
                        bottomBtn = bottomBtn,
                        failRef = failRef
                    )
                }
            }

            is RecordOptionResult.SummaryOption -> {
                if (tab == Tab.Summary) {
                    TabSummary(
                        viewModel = viewModel,
                        transOrSummaryFail = transOrSummaryFail,
                        title = title,
                        progressView = progressView,
                        webContent = webContent,
                        summaryTips = summaryTips,
                        bottomBtn = bottomBtn,
                        failRef = failRef
                    )
                } else if (tab == Tab.Transcribe) {
                    TabTranscription(
                        viewModel,
                        transOrSummaryFail,
                        title,
                        list,
                        progressView,
                        emptyView,
                        bottomBtn,
                        failRef
                    )
                }
            }

            is RecordOptionResult.FinishComplete -> { viewModel.switchTab(tab) }
            else -> Unit
        }
        BottomArea(
            bottomBtn,
            viewModel,
            onPlayPause = {
                viewModel.sendEvent(AudioTranscriptionEvent.PlayOrPause)
                O95Statistic.clickTranscribeAndSummaryOption(
                    name = clickBackName(viewModel.tabLiveData.value),
                    positionName = "play_pause_button"
                )
            },
            onFast = {
                viewModel.sendEvent(AudioTranscriptionEvent.FastForward)
            },
            onRewind = {
                viewModel.sendEvent(AudioTranscriptionEvent.Rewind)
            }
        )
        InitDialog(viewModel, dialogState)
    }
}

@Composable
private fun ConstraintLayoutScope.TabTranscription(
    viewModel: AudioTranscriptionViewModel,
    transOrSummaryFail: RecordSummaryFail?,
    title: ConstrainedLayoutReference,
    list: ConstrainedLayoutReference,
    progressView: ConstrainedLayoutReference,
    emptyView: ConstrainedLayoutReference,
    bottomBtn: ConstrainedLayoutReference,
    failRef: ConstrainedLayoutReference
) {
    if (viewModel.isReTranscription) {
        TranscriptionInProgress(
            progressView,
            topRef = title,
            bottomRef = bottomBtn,
            loadingType = 1
        )
    } else if (transOrSummaryFail is RecordSummaryFail.TranslateFail) {
        TransliterateFail(
            viewModel,
            transOrSummaryFail,
            topRef = title,
            bottomRef = bottomBtn,
            failRef
        )
    } else {
        ShowTranscription(viewModel, title, list, emptyView, bottomBtn, failRef)
    }
}

@Composable
private fun ConstraintLayoutScope.TabSummary(
    viewModel: AudioTranscriptionViewModel,
    transOrSummaryFail: RecordSummaryFail?,
    title: ConstrainedLayoutReference,
    progressView: ConstrainedLayoutReference,
    webContent: ConstrainedLayoutReference,
    summaryTips: ConstrainedLayoutReference,
    bottomBtn: ConstrainedLayoutReference,
    failRef: ConstrainedLayoutReference
) {
    if (viewModel.isReSummary) {
        TranscriptionInProgress(
            progressView,
            topRef = title,
            bottomRef = bottomBtn,
            loadingType = 2
        )
    } else if (transOrSummaryFail is RecordSummaryFail.SummaryFail) {
        TransliterateFail(
            viewModel,
            transOrSummaryFail,
            topRef = title,
            bottomRef = bottomBtn,
            transcriptionFail = failRef
        )
    } else {
        ShowSummary(
            viewModel = viewModel,
            titleRef = title,
            webContent = webContent,
            summaryTips = summaryTips,
            bottomBtn = bottomBtn,
            progressView = progressView,
            failRef = failRef
        )
        O95Statistic.exposeTranscriptionAndSummaryPage("Summary_Content")
    }
}

@Composable
private fun ConstraintLayoutScope.ShowTranscription(
    viewModel: AudioTranscriptionViewModel,
    title: ConstrainedLayoutReference,
    list: ConstrainedLayoutReference,
    emptyView: ConstrainedLayoutReference,
    bottomBtn: ConstrainedLayoutReference,
    failRef: ConstrainedLayoutReference
) {
    // 判断旧数据需要展示提示框
    val phrases = viewModel.transcribePhrasesList
    val hasSentences = phrases.all { it.phrase.sentences.isNotNullOrEmpty() }
    if (!hasSentences && !viewModel.hasShownTranslateDialog.value) {
        viewModel.showNewTranslateDialog(true)
        viewModel._hasShownTranslateDialog.value = true
    }
    if (viewModel.transcribePhrasesList.isNotEmpty()) {
        TranscriptItemList(
            list,
            title,
            bottomBtn,
            viewModel.transcribePhrasesList,
            viewModel
        )
        O95Statistic.exposeTranscriptionAndSummaryPage("Transcription_Content")
    } else if (viewModel.transcribeErrorCode != 0) {
        TransliterateFail(
            viewModel,
            RecordSummaryFail.TranslateFail(viewModel.transcribeErrorCode),
            topRef = title,
            bottomRef = bottomBtn,
            transcriptionFail = failRef
        )
    } else {
        TranscriptionTips(
            title,
            emptyView,
            onGenerateClick = { viewModel.showTranscriptionSettingsDialog(true) }
        )
    }
}

@Composable
private fun ConstraintLayoutScope.ShowSummary(
    viewModel: AudioTranscriptionViewModel,
    titleRef: ConstrainedLayoutReference,
    webContent: ConstrainedLayoutReference,
    summaryTips: ConstrainedLayoutReference,
    bottomBtn: ConstrainedLayoutReference,
    progressView: ConstrainedLayoutReference,
    failRef: ConstrainedLayoutReference,
    template: String = viewModel.summaryTemplate,
    summaryRequestContent: String = viewModel.generateSummaryRequestJson()
) {
    if (viewModel.summaryErrorCode != 0) {
        TransliterateFail(
            viewModel,
            RecordSummaryFail.SummaryFail(viewModel.summaryErrorCode),
            topRef = titleRef,
            bottomRef = bottomBtn,
            transcriptionFail = failRef
        )
    } else {
        val summaryTitle = viewModel.transcriptionSummaryTitle.value
        val summaryContent = viewModel.transcribeSummary.value
        Timber.i("showSummary title:${summaryTitle.isNotEmpty()}, content:${summaryContent.isNotEmpty()}")
        if (summaryTitle.isNotEmpty() && summaryContent.isNotEmpty()) {
            val showTips = viewModel.transcribePhrasesList.any { it.isFocusSpeaker }
            if (showTips) ReSummaryTips(viewModel, summaryTips, titleRef)
            WebViewWithLifecycle(
                topRef = if (showTips) summaryTips else titleRef,
                webContent = webContent,
                bottomBtn = bottomBtn,
                viewModel = viewModel,
                content = summaryRequestContent
            )
        } else {
            TranscriptionInProgress(
                progressView,
                topRef = titleRef,
                bottomRef = bottomBtn,
                loadingType = 2
            )
        }
    }
}

private fun clickBackName(tab: Tab): String {
    return if (tab == Tab.Transcribe) {
        "Transcription_Content"
    } else {
        "Summary_Content"
    }
}

@Composable
private fun ConstraintLayoutScope.ReSummaryTips(
    viewModel: AudioTranscriptionViewModel,
    summaryTips: ConstrainedLayoutReference,
    title: ConstrainedLayoutReference
) {
    val alphaAnimate = remember { Animatable(0f) }
    val alphaState = viewModel.alphaState
    LaunchedEffect(Unit) {
        if (alphaState.value) {
            alphaAnimate.animateTo(
                targetValue = 1f,
                animationSpec = tween(durationMillis = 1000)
            )
            alphaState.value = false
        }
    }
    val alpha by alphaAnimate.asState()

    Column(
        modifier = Modifier
            .padding(top = Dp_20, start = Dp_20, end = Dp_20)
            .alpha(alpha = if (!alphaState.value) 1f else alpha)
            .constrainAs(summaryTips) {
                top.linkTo(title.bottom)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                height = Dimension.wrapContent
            }
    ) {
        Row(
            modifier = Modifier
                .background(
                    color = Color18191A,
                    shape = RoundedCornerShape(Dp_12)
                )
                .fillMaxWidth()
                .wrapContentHeight()
                .clickable { viewModel.showReSummarizeDialog(true) },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                stringResource(R.string.text_retry_summary),
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                color = ColorWhite,
                modifier = Modifier
                    .padding(all = Dp_14)
                    .weight(1f)
            )
            Image(
                painter = painterResource(R.drawable.icon_summary_right_arrow),
                contentDescription = "right arrow",
                modifier = Modifier.padding(end = Dp_14)
            )
        }
    }
}

@Composable
private fun ConstraintLayoutScope.TranscriptItemList(
    list: ConstrainedLayoutReference,
    title: ConstrainedLayoutReference,
    bottomBtn: ConstrainedLayoutReference,
    phrases: List<AudioTranscriptionViewModel.SpeakPhrase>,
    viewModel: AudioTranscriptionViewModel
) {
    val listState = rememberLazyListState()
    val state by viewModel.mState.collectAsState()
    LazyColumn(
        state = listState,
        modifier = Modifier
            .padding(horizontal = Dp_20)
            .constrainAs(list) {
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                top.linkTo(title.bottom)
                bottom.linkTo(bottomBtn.top, margin = Dp_20)
                height = Dimension.fillToConstraints
            },
        verticalArrangement = Arrangement.spacedBy(Dp_20),
        contentPadding = PaddingValues(top = Dp_20)
    ) {
        items(phrases, key = { it.objId }) { item ->
            SpeakerText(
                speakerName = item.speakName,
                speakerNameNum = item.phrase.speakerId,
                isFocusSpeaker = item.isFocusSpeaker,
                content = item.phrase.text,
                viewModel = viewModel,
                phrase = item.phrase,
                onSpeakerClick = {
                        speakerName ->
                    val speaker = AudioTranscriptionViewModel.Speaker(
                        item.objId,
                        change = true,
                        name = speakerName,
                        isFocusSpeaker = item.isFocusSpeaker
                    )
                    viewModel.showFixSpeakerName(speaker)
                },
                onTimeClick = {
                    viewModel.sendEvent(AudioTranscriptionEvent.Play)
                    viewModel.sendEvent(
                        AudioTranscriptionEvent.SeekTo(
                            item.phrase.offsetMilliseconds.toFloat() / viewModel.controller.duration.value
                        )
                    )
                },
                onSentenceClick = { sentence ->
                    viewModel.sendEvent(
                        AudioTranscriptionEvent.SeekTo(
                            sentence.offsetMilliseconds.toFloat() / viewModel.controller.duration.value
                        )
                    )
                }
            )
        }
    }
    // 根据语句的时间偏移量来滚动定位到所在段落，呈现在屏幕1/3处
    LaunchedEffect(viewModel.highlightedSentenceOffset.value) {
        val id = viewModel.highlightedPhraseId.value ?: return@LaunchedEffect
        val index = phrases.indexOfFirst { it.objId == id }
        if (index >= 0) {
            if (!state.isPlaying) {
                listState.animateScrollToItem(index, scrollOffset = 0)
                return@LaunchedEffect
            }

            val layoutInfo = listState.layoutInfo
            val screenHeight = layoutInfo.viewportSize.height
            val itemInfo = layoutInfo.visibleItemsInfo.find { it.index == index }

            val itemBottom = itemInfo?.let { it.offset + it.size }
            val twoThirds = screenHeight * 2 / 3
            if (itemBottom != null && itemBottom > twoThirds) {
                listState.animateScrollToItem(index, scrollOffset = 0)
            }
        }
    }

    LaunchedEffect(viewModel.isDragging.value) {
        val id = viewModel.highlightedPhraseId.value ?: return@LaunchedEffect
        val index = phrases.indexOfFirst { it.objId == id }
        if (index >= 0) {
            listState.animateScrollToItem(index, scrollOffset = 0)
        }
    }
}

@SuppressLint("DefaultLocale")
fun formatTimestamp(millis: Long): String {
    val totalSeconds = millis / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600 / 60).toInt()
    val seconds = (totalSeconds % 60).toInt()

    return if (hours > 0) {
        String.format("%02d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%02d:%02d", minutes, seconds)
    }
}

@SuppressLint("RememberReturnType")
@Composable
fun SpeakerText(
    speakerName: String,
    speakerNameNum: Int,
    isFocusSpeaker: Boolean = false,
    content: String,
    phrase: Phrase,
    viewModel: AudioTranscriptionViewModel?,
    onSpeakerClick: (speaker: String) -> Unit, // 新增点击回调
    onTimeClick: (startTime: Long) -> Unit, // 时间戳点击回调
    onSentenceClick: (Sentence) -> Unit // 语句点击回调
) {
    // 定义注解标签
    val speaker = if (speakerName.isNotNullOrEmpty()) {
        speakerName
    } else {
        stringResource(R.string.text_transcription_speaker, speakerNameNum)
    }

    Column(modifier = Modifier.fillMaxWidth()) {
        if (speakerNameNum > 0) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (isFocusSpeaker) {
                    Icon(
                        painter = painterResource(R.mipmap.ic_focus_speaker),
                        contentDescription = null,
                        tint = colorResource(R.color.white),
                        modifier = Modifier.size(Dp_20)
                    )
                    Spacer(modifier = Modifier.width(Dp_8))
                }

                // 说话人名称 + 编辑图标（可点击）
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        modifier = Modifier.clickable { onSpeakerClick(speaker) },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = speaker,
                            color = SpeakerTextDefaults.getColor(speaker),
                            fontSize = Sp_16,
                            fontWeight = FontWeight.W500
                        )
                        Spacer(modifier = Modifier.width(Dp_4))
                        Icon(
                            painter = painterResource(id = R.drawable.icon_edit),
                            contentDescription = null,
                            tint = colorResource(R.color.white_60),
                            modifier = Modifier.size(Dp_20)
                        )
                    }
                }

                // 时间戳（点击触发）
                Text(
                    text = formatTimestamp(phrase.offsetMilliseconds.toLong()),
                    color = colorResource(R.color.white_60),
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    modifier = Modifier.clickable {
                        onTimeClick(phrase.offsetMilliseconds.toLong())
                    }
                )
            }
            Spacer(Modifier.height(Dp_8))
        }
        if (phrase.sentences.isNotNullOrEmpty()) {
            // 语句高亮处理
            val annotatedSentenceString = remember(
                phrase.sentences,
                viewModel?.highlightedSentenceOffset?.value
            ) {
                buildAnnotatedString {
                    phrase.sentences?.forEach { sentence ->
                        val isSentenceHighlighted =
                            viewModel?.highlightedSentenceOffset?.value == sentence.offsetMilliseconds
                        pushStringAnnotation(tag = "Sentence", annotation = sentence.offsetMilliseconds.toString())
                        if (viewModel != null) {
                            withStyle(
                                style = SpanStyle(
                                    color = if (isSentenceHighlighted) Color55D8E4 else Color.White
                                )
                            ) {
                                append(sentence.text)
                            }
                            pop()
                        }
                    }
                }
            }
            ClickableText(
                text = annotatedSentenceString,
                onClick = { offset ->
                    annotatedSentenceString.getStringAnnotations(tag = "Sentence", start = offset, end = offset)
                        .firstOrNull()
                        ?.let { it ->
                            val sentenceOffset = it.item.toLong()
                            val sentence = phrase.sentences?.find { it.offsetMilliseconds == sentenceOffset }
                            sentence?.let(onSentenceClick)
                        }
                },
                modifier = Modifier.fillMaxWidth()
            )
        } else {
            Text(
                text = content,
                style = TextStyle(color = ColorWhite, fontSize = Sp_16),
                lineHeight = Sp_28
            )
        }
    }
}

private fun checkNetState(): Boolean {
    if (!NetworkMonitor.isConnected()) {
        LibBaseApplication.instance.toast(R.string.record_error_network)
        return false
    }
    return true
}

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
private fun InitDialog(viewModel: AudioTranscriptionViewModel, dialogState: DialogState) {
    TranscriptionSettingsDialog(
        visible = dialogState.isShowDialog,
        viewModel,
        onDismiss = {
            viewModel.showTranscriptionSettingsDialog(false)
            resetDistinguishSpeakersAndTemplate(viewModel)
        },
        onClick = {
            if (checkNetState()) {
                viewModel.summaryTemplate = viewModel.currentCurrentTemplate.value
                viewModel.sendEvent(AudioTranscriptionEvent.RequestTranscribe)
                resetDistinguishSpeakersAndTemplate(viewModel)
                O95Statistic.clickTranscribeOption("transcription_begin_button")
            }
        },
        onSelectLanguage = {
            viewModel.showSelectLanguageDialog(true)
            O95Statistic.clickTranscribeOption("record_language_button")
        },
        onDistinguishSpeakers = {
            viewModel.updateDistinguishSpeakers(it)
            O95Statistic.clickTranscribeOption("distinguish_speaker_switch")
        }
    )

    ReTranscribeDialog(
        viewModel,
        visible = dialogState.isShowReTranscribe,
        text = viewModel.currentLanguage,
        onDismiss = {
            viewModel.showReTranscribeDialog(false)
            resetDistinguishSpeakersAndTemplate(viewModel)
        },
        onClick = {
            if (checkNetState()) {
                val tab = viewModel.tabLiveData.value
                if (tab != Tab.Transcribe) {
                    viewModel.switchTab(Tab.Transcribe)
                }
                viewModel.sendEvent(AudioTranscriptionEvent.ReTranscribe)
                resetDistinguishSpeakersAndTemplate(viewModel)
            }
        },
        onSelectLanguage = { viewModel.showSelectLanguageDialog(true) },
        onDistinguishSpeakers = { viewModel.updateDistinguishSpeakers(it) }
    )

    ReSummarizeDialog(
        visible = dialogState.isShowReSummarize,
        viewModel,
        onDismiss = {
            viewModel.showReSummarizeDialog(false)
            resetDistinguishSpeakersAndTemplate(viewModel)
        },
        onClick = {
            if (checkNetState()) {
                val tab = viewModel.tabLiveData.value
                if (tab != Tab.Summary) {
                    viewModel.switchTab(Tab.Summary)
                }
                val template = viewModel.currentCurrentTemplate.value
                viewModel.sendEvent(AudioTranscriptionEvent.RequestSummary(template))
                resetDistinguishSpeakersAndTemplate(viewModel)
            }
        }
    )

    MenuItemPopup(
        viewModel,
        visible = dialogState.showMenu,
        onDismiss = { viewModel.showMenu(false) },
        onReTranscribeClick = {
            viewModel.showReTranscribeDialog(true)
            O95Statistic.clickTranscribeAndSummaryOption(
                name = clickBackName(viewModel.tabLiveData.value),
                positionName = "rewrite_button"
            )
        },
        onReSummarizeClick = {
            if (viewModel.transcribePhrases.isEmpty()) {
                LibBaseApplication.instance.toast(R.string.record_wait_transcribe_finish)
                return@MenuItemPopup
            }
            viewModel.showReSummarizeDialog(true)
            O95Statistic.clickTranscribeAndSummaryOption(
                name = clickBackName(viewModel.tabLiveData.value),
                positionName = "re_summary_button"
            )
        },
        onDeleteClick = {
            viewModel.showDeleteDialog(true)
            O95Statistic.clickTranscribeAndSummaryOption(
                name = clickBackName(viewModel.tabLiveData.value),
                positionName = "write_summary_delete"
            )
        }
    )

    MenuShareItemPopup(
        viewModel,
        visible = dialogState.showShareMenu,
        onDismiss = { viewModel.showShareMenu(false) },
        buttonConfig = ButtonConfig.OneButton(
            ButtonParams(text = stringResource(id = R.string.cancel)) {
                viewModel.showShareMenu(false)
            }
        ),
        onShareClick = { audioTranscriptionEvent ->
            viewModel.sendEvent(audioTranscriptionEvent)
        }
    )

    MenuShareLinkPopup(
        viewModel = viewModel,
        visible = dialogState.showShareLinkMenu,
        onDismiss = { viewModel.showShareLinkMenu(false) },
        buttonConfig = ButtonConfig.ThreeButton(
            button1 = ButtonParams(
                text = stringResource(id = R.string.text_share),
                enableColors = listOf(Color17CBFF, Color26EAD9),
                disableColors = listOf(Color17CBFF_30, Color26EAD9_30)
            ),
            button2 = ButtonParams(text = stringResource(id = R.string.text_copy_link)),
            button3 = ButtonParams(text = stringResource(id = R.string.cancel))
        )
    )

    DeleteDialog(
        isShowDelete = dialogState.isShowDelete,
        onDeleteAllClick = {
            viewModel.showDeleteDialog(false)
            viewModel.toDeleteMediaFile(true)
        },
        { viewModel.showDeleteDialog(false) }
    )

    ListOfRecordingLanguagesDialog(
        visible = dialogState.isShowSelectLanguage,
        viewModel,
        onConfirm = {
            viewModel.currentLanguage.value = it
        },
        onDismiss = { viewModel.showSelectLanguageDialog(false) }
    )

    val changeSpeaker = dialogState.showFixSpeakerName.value
    EditSingleSpeakerNameDialog(
        viewModel = viewModel,
        titleId = R.string.text_rename_speaker_title,
        inputText = changeSpeaker.name,
        placeholder = changeSpeaker.name,
        visible = changeSpeaker.change,
        maxLength = SPEAKER_NAME_MAX,
        buttonConfig = ButtonConfig.TwoButton(
            ButtonParams(text = stringResource(R.string.action_confirm)),
            ButtonParams(text = stringResource(id = R.string.cancel))
        ),
        onDismiss = {
            val speaker =
                AudioTranscriptionViewModel.Speaker(
                    changeSpeaker.objId,
                    false,
                    changeSpeaker.name
                )
            viewModel.showFixSpeakerName(speaker)
        },
        onShowQuickEditDialog = {
            val speaker =
                AudioTranscriptionViewModel.Speaker(changeSpeaker.objId, false, changeSpeaker.name)
            viewModel.apply {
                showFixSpeakerName(speaker)
                showQuickFixAllDialog(true)
            }
        },
        onConfirm = { speakerName: String, isFocusSpeaker: Boolean, fixAll: Boolean ->
            val speaker =
                AudioTranscriptionViewModel.Speaker(
                    objId = changeSpeaker.objId,
                    change = false,
                    name = speakerName,
                    isFocusSpeaker = isFocusSpeaker
                )
            viewModel.apply {
                showFixSpeakerName(speaker)
                launch(Dispatchers.IO) {
                    changeSpeakerName(
                        srcSpeakerName = changeSpeaker.name,
                        objIds = listOf(speaker.objId),
                        dstSpeakerName = speaker.name,
                        fixAll = fixAll
                    )
                    markAsFocusSpeaker(
                        objIds = listOf(speaker.objId),
                        srcSpeakerName = changeSpeaker.name,
                        isFocusSpeaker,
                        fixAll = true
                    )
                }
            }
        }
    )

    if (viewModel.transcribePhrasesList.isNotEmpty()) {
        QuickEditAllSpeakersDialog(
            viewModel = viewModel,
            visible = dialogState.showQuickFixAllDialog.value,
            maxLength = SPEAKER_NAME_MAX,
            phraseList = viewModel.transcribePhrasesList,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(R.string.action_confirm)),
                ButtonParams(text = stringResource(id = R.string.cancel))
            ),
            onDismiss = { viewModel.showQuickFixAllDialog(false) },
            onPlayStart = { offsetMs, durationMs ->
                viewModel.sendEvent(AudioTranscriptionEvent.PlayClipForDuration(offsetMs, durationMs))
            },
            onPlayPause = {
                viewModel.sendEvent(AudioTranscriptionEvent.Pause)
            },
            onConfirm = { updateList ->
                viewModel.apply {
                    showQuickFixAllDialog(false)
                    launch(Dispatchers.IO) {
                        updateList.forEach { updateItem ->
                            changeSpeakerName(
                                srcSpeakerName = updateItem.oldName,
                                objIds = updateItem.affectedObjIds,
                                dstSpeakerName = updateItem.newName,
                                fixAll = false
                            )
                            markAsFocusSpeaker(
                                objIds = updateItem.affectedObjIds,
                                srcSpeakerName = updateItem.newName,
                                updateItem.isFocus,
                                fixAll = false
                            )
                        }
                    }
                }
            }
        )
    }

    LoadingDialog(
        loadingType = viewModel.loadingType.value ?: LoadingType.AUDIO,
        tip = viewModel.loadingDesc.value ?: "",
        visible = viewModel.isShowLoading.value
    ) { loadingType ->
        viewModel.setLoading(false)
        when (loadingType) {
            LoadingType.AUDIO -> {
                OggDecodeHelper.oggConvertCancel()
            }

            LoadingType.LINK -> {
            }

            LoadingType.IMAGE -> {
                viewModel.apply {
                    imageCreateJob?.cancel()
                    imageCreateJob = null
                }
            }
        }
    }

    NewTranslateDialog(
        visible = dialogState.isShowNewTranslateDialog,
        onDismiss = {
            viewModel.showNewTranslateDialog(false)
        }
    )
}

private fun resetDistinguishSpeakersAndTemplate(viewModel: AudioTranscriptionViewModel) {
    viewModel.viewModelScope.launch {
        delay(DISTINGUISH_SPEAKERS)
        viewModel.isDistinguishSpeakers.value = true
        viewModel.currentCurrentTemplate.value = "abstractAutopilot"
        viewModel.currentLanguageValue.value = Language.ZH_CN
        viewModel.currentLanguage.value = ""
    }
}

fun handleEffect(
    viewModel: AudioTranscriptionViewModel,
    effect: AudioTranscriptionEffect?,
    context: Context,
    scope: CoroutineScope,
    navigator: NavController
) {
    Timber.i("handleEffect $effect")
    when (effect) {
        is AudioTranscriptionEffect.ShareAudioItem -> {
            effect.bean.let {
                val filePath = it.path
                filePath?.apply {
                    OggDecodeHelper.oggDecode(context, this) { showLoading, convertStatus ->
                        if (showLoading) {
                            viewModel.setLoading(
                                isLoading = true,
                                type = LoadingType.AUDIO,
                                desc = context.getString(R.string.text_share_convert_mp3_tip)
                            )
                        } else {
                            viewModel.setLoading(false)
                            if (convertStatus) {
                                RecordShare.share(context, it, this)
                            } else {
                                LibBaseApplication.instance.toast(R.string.record_share_convert_tip)
                            }
                        }
                    }
                }
            }
        }

        is AudioTranscriptionEffect.ShareLink -> {
            viewModel.setLoading(false)
            if (effect.success) {
                IntentUtils.shareUrl(
                    context = context,
                    url = WebPageHelper.getShareLink(effect.link),
                    title = "分享到"
                )
            } else {
                // 失败方案，先弹toast，等产品方案
                context.toast("链接生成失败，请稍后重试")
            }
        }

        is AudioTranscriptionEffect.ShareImage -> {
            if (!effect.success || effect.image == null) {
                viewModel.setLoading(false)
                context.toast("图片生成失败，请稍后重试")
                return
            }

            val bitmap = effect.image

            viewModel.apply {
                imageCreateJob?.cancel()
                if (effect.needPreview) {
                    imageCreateJob = scope.launch(Dispatchers.IO) {
                        ImageShare.bitmapToFile(
                            context = context,
                            bitmap = bitmap,
                            fileName = viewModel.mState.value.currentItem?.fileName
                        )
                        val path = ImageShare.getFilePath()
                        withContext(Dispatchers.Main) {
                            viewModel.setLoading(false)
                            if (path.isEmpty()) {
                                context.toast("图片生成失败，请稍后重试")
                            } else {
                                PageRouter.navigateToImagePreviewFragment(
                                    navigator = navigator,
                                    imagePath = path
                                )
                            }
                        }
                    }
                } else {
                    imageCreateJob = scope.launch(Dispatchers.IO) {
                        val fileName = viewModel.mState.value.currentItem?.fileName
                            ?: System.currentTimeMillis().toString()
                        ImageShare.share(
                            context = context,
                            bitmap = bitmap,
                            fileName = fileName,
                            onCompleted = { result ->
                                viewModel.setLoading(false)
                                if (!result) {
                                    context.toast("图片分享失败，请稍后重试")
                                }
                            }
                        )
                    }
                }
            }
        }

        is AudioTranscriptionEffect.Delete -> {
            navigator.pop()
        }

        else -> Unit
    }
}

@Composable
fun ActionTitleBar(
    viewModel: AudioTranscriptionViewModel,
    modifier: Modifier,
    titleModifier: Modifier = Modifier,
    onClick: () -> Unit = {},
    onShareClick: () -> Unit = {},
    onMoreClick: () -> Unit = {}
) {
    ConstraintLayout(modifier = modifier.fillMaxWidth()) {
        val (backIcon, shareIcon, moreIcon, textTitle) = createRefs()
        Image(
            painter = painterResource(id = R.drawable.ic_back_white),
            contentDescription = "back",
            modifier = Modifier
                .constrainAs(backIcon) {
                    top.linkTo(parent.top, margin = Dp_13)
                    start.linkTo(parent.start, margin = Dp_20)
                }
                .clickable {
                    onClick.invoke()
                }
                .size(Dp_32)
                .alpha(1f)
        )

        Image(
            painter = painterResource(R.drawable.icon_share),
            contentDescription = "share",
            modifier = Modifier
                .constrainAs(shareIcon) {
                    top.linkTo(parent.top, margin = Dp_13)
                    end.linkTo(moreIcon.start, margin = Dp_20)
                }
                .clickable {
                    onShareClick.invoke()
                }
                .size(Dp_32)
        )
        Image(
            painter = painterResource(id = R.drawable.icon_more),
            contentDescription = "more settings",
            modifier = Modifier
                .constrainAs(moreIcon) {
                    top.linkTo(parent.top, margin = Dp_13)
                    end.linkTo(parent.end, margin = Dp_20)
                }
                .clickable {
                    onMoreClick.invoke()
                }
                .size(Dp_32)
        )

        if (viewModel.isCanShowTab()) {
            InitTab(titleModifier, textTitle, backIcon, viewModel)
        }
    }
}

@Composable
private fun ConstraintLayoutScope.InitTab(
    modifier: Modifier,
    textTitle: ConstrainedLayoutReference,
    backIcon: ConstrainedLayoutReference,
    viewModel: AudioTranscriptionViewModel
) {
    val tab = viewModel.tabLiveData.value
    LaunchedEffect(Unit) {
        if (tab == Tab.Transcribe) {
            viewModel.switchTab(Tab.Transcribe)
        }
    }
    Row(
        modifier = modifier.constrainAs(textTitle) {
            start.linkTo(parent.start, margin = Dp_20)
            top.linkTo(backIcon.bottom, margin = Dp_13)
            end.linkTo(parent.end, margin = Dp_20)
            width = Dimension.fillToConstraints
        }
    ) {
        Text(
            stringResource(R.string.title_transcription),
            style = TextStyle(
                fontSize = Sp_28,
                fontWeight = FontWeight.W600,
                color = if (tab == Tab.Transcribe) Color.White else Color.Gray
            ),
            modifier = Modifier.clickable {
                viewModel.switchTab(Tab.Transcribe)
            }
        )
        Spacer(modifier = Modifier.width(Dp_28))
        Text(
            stringResource(R.string.title_summary),
            style = TextStyle(
                fontSize = Sp_28,
                fontWeight = FontWeight.W600,
                color = if (tab == Tab.Summary) Color.White else Color.Gray
            ),
            modifier = Modifier.clickable {
                viewModel.switchTab(Tab.Summary)
            }
        )
    }
}

/**
 * 上传，转写，总结进行中动画.
 * @param loadingType 0 -> uploading, 1 -> transcription, 2 -> summary
 */
@Composable
private fun ConstraintLayoutScope.TranscriptionInProgress(
    progressView: ConstrainedLayoutReference,
    topRef: ConstrainedLayoutReference,
    bottomRef: ConstrainedLayoutReference,
    loadingType: Int
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .background(ColorBlack)
            .padding(horizontal = Dp_28)
            .constrainAs(progressView) {
                top.linkTo(topRef.bottom)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                bottom.linkTo(bottomRef.top)
                height = Dimension.fillToConstraints
            }
    ) {
        val (loading, title, desc) = createRefs()
        val composition by rememberLottieComposition(
            LottieCompositionSpec.Asset("loading.json")
        )
        LottieAnimation(
            composition,
            iterations = LottieConstants.IterateForever,
            modifier = Modifier
                .constrainAs(loading) {
                    top.linkTo(parent.top, margin = Dp_150)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
                .size(Dp_70)
        )
        Text(
            text = stringResource(
                when (loadingType) {
                    0 -> R.string.text_media_file_uploading
                    2 -> R.string.text_summary_in_progress
                    else -> R.string.text_transcription_in_progress
                }
            ),
            fontSize = Sp_16,
            fontWeight = FontWeight.W500,
            color = Color.White,
            modifier = Modifier.constrainAs(title) {
                top.linkTo(loading.bottom, margin = Dp_12)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }
        )
        Text(
            text = stringResource(
                when (loadingType) {
                    0 -> R.string.text_media_file_uploading_warning_tips
                    else -> R.string.text_transcription_waiting_tips
                }
            ),
            fontSize = Sp_13,
            fontWeight = FontWeight.W400,
            color = if (loadingType == 0) {
                colorResource(R.color.color_55D8E4)
            } else {
                Color.White.copy(alpha = 0.6f)
            },
            lineHeight = Sp_21,
            modifier = Modifier.constrainAs(desc) {
                top.linkTo(title.bottom, margin = Dp_20)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }
        )
    }
}

@Composable
private fun ConstraintLayoutScope.TranscriptionTips(
    title: ConstrainedLayoutReference,
    emptyView: ConstrainedLayoutReference,
    onGenerateClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_28)
            .constrainAs(emptyView) {
                top.linkTo(title.bottom, margin = Dp_150)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            painter = painterResource(R.mipmap.icon_summary_tips),
            contentDescription = "document icon",
            modifier = Modifier.size(Dp_100)
        )
        Spacer(Modifier.height(Dp_12))
        Text(
            text = stringResource(id = R.string.text_audio_trans_tip),
            fontSize = Sp_16,
            fontWeight = FontWeight.W500,
            color = ColorWhite
        )
        Spacer(Modifier.height(Dp_12))
        Text(
            text = stringResource(id = R.string.text_audio_trans_desc),
            fontSize = Sp_13,
            fontWeight = FontWeight.W400,
            color = ColorWhite50,
            lineHeight = Sp_21,
            textAlign = TextAlign.Center, // 水平居中
            modifier = Modifier.fillMaxWidth() // 占满宽度才能居中有效
        )
        Spacer(Modifier.height(Dp_40))
        SubmitButton(
            subTitle = stringResource(R.string.text_generate_now),
            enable = true,
            textColor = ColorBlack,
            enableColors = listOf(Color26EAD9, Color17CBFF),
            disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
            modifier = Modifier.size(Dp_155, Dp_50)
        ) {
            if (checkNetState()) {
                onGenerateClick.invoke()
                O95Statistic.clickCreateTranscribeOption()
            }
        }
    }
}

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun PlaybackSpeedMenu(viewModel: AudioTranscriptionViewModel, dialogState: DialogState) {
    val speedOptions = viewModel.speedOptions
    val currentSpeed = viewModel.playbackSpeed.value
    BottomSheet(
        visible = dialogState.isShowSpeedMenu.value,
        onDismiss = { dialogState.isShowSpeedMenu.value = false }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_469)
                .padding(vertical = Dp_30)
                .background(color = Color18191A, shape = RoundedCornerShape(Dp_16))
        ) {
            Text(
                text = stringResource(id = R.string.speed_select_title),
                fontSize = Sp_16,
                fontWeight = FontWeight.W500,
                color = Color.White,
                modifier = Modifier
                    .fillMaxWidth(),
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(Dp_20))
            // 速度选项列表
            speedOptions.forEach { speed ->
                val isSelected = currentSpeed == speed
                PlaybackSpeedItem(
                    speed = speed,
                    selected = isSelected,
                    onClick = {
                        viewModel.sendEvent(AudioTranscriptionEvent.SpeedSelected(speed))
                        dialogState.isShowSpeedMenu.value = false
                    }
                )
            }
            Spacer(modifier = Modifier.height(Dp_30))
            BottomButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Dp_50)
                    .padding(horizontal = Dp_30),
                onDismiss = {
                    dialogState.isShowSpeedMenu.value = false
                }
            )
        }
    }
}

@Composable
fun BottomButton(
    modifier: Modifier,
    onDismiss: () -> Unit
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(Dp_16))
            .background(Color222425)
            .clickDebounce { onDismiss.invoke() }
    ) {
        Text(
            text = stringResource(id = R.string.speed_select_button),
            modifier = Modifier.align(Alignment.Center),
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_16,
                fontWeight = FontWeight.W500,
                fontFamily = FontFamily.Default,
                textAlign = TextAlign.Center
            )
        )
    }
}

@Composable
private fun PlaybackSpeedItem(
    speed: Float,
    selected: Boolean,
    onClick: () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_28)
            .height(Dp_58)
            .clickable(
                interactionSource = interactionSource,
                indication = rememberRipple(bounded = true)
            ) { onClick() },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "x$speed",
            fontSize = Sp_16,
            color = Color.White,
            fontWeight = FontWeight.W400,
            modifier = Modifier
        )
        Box(
            modifier = Modifier
                .size(Dp_25)
                .align(Alignment.CenterVertically)
        ) {
            Image(
                painter = painterResource(
                    id = if (selected || isPressed) R.drawable.ic_selected else R.drawable.ic_unselect
                ),
                contentDescription = null,
                modifier = Modifier
            )
        }
    }
}

@Composable
private fun ConstraintLayoutScope.BottomArea(
    bottomBtn: ConstrainedLayoutReference,
    viewModel: AudioTranscriptionViewModel,
    onPlayPause: () -> Unit,
    onRewind: () -> Unit,
    onFast: () -> Unit
) {
    val state by viewModel.mState.collectAsState()
    val isDragging by remember { mutableStateOf(false) }
    val durationMillis = remember(state.currentItem) {
        state.currentItem?.duration?.times(1000) ?: 0L
    }
    val formattedDuration = remember(durationMillis) {
        DateTimeUtils.videoDuration(durationMillis)
    }
    val progressTime = state.playbackProgress
    val progress = if (state.totalDuration > 0) {
        (state.playbackProgress.toFloat() / state.totalDuration.toFloat())
    } else {
        0f
    }
    val dialogState by viewModel.dialogState.collectAsState()
    val currentSpeed = viewModel.playbackSpeed.collectAsState()
    val (rowFir, rowSec) = createRefs()
    ConstraintLayout(
        modifier = Modifier.constrainAs(bottomBtn) {
            start.linkTo(parent.start)
            end.linkTo(parent.end)
            bottom.linkTo(parent.bottom)
        }
            .fillMaxWidth()
            .height(Dp_123)
            .background(color = Color18191A, shape = RoundedCornerShape(Dp_16))
    ) {
        Row(
            modifier = Modifier
                .padding(horizontal = Dp_20)
                .constrainAs(rowFir) {
                    centerHorizontallyTo(parent)
                    top.linkTo(parent.top, margin = Dp_20)
                    bottom.linkTo(rowSec.top)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            ConstraintLayout(
                modifier = Modifier.fillMaxWidth()
            ) {
                val (startTime, progressBar, endTime) = createRefs()
                Text(
                    text = DateTimeUtils.videoDuration(progressTime),
                    color = if (isDragging) Color.White else Color.White.copy(alpha = 0.4f),
                    style = TextStyle(
                        fontSize = Sp_13,
                        fontWeight = FontWeight.W400,
                        textAlign = TextAlign.Start,
                        fontFamily = FontFamily.Serif // 使用等宽字体
                    ),
                    modifier = Modifier.constrainAs(startTime) {
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        start.linkTo(parent.start)
                    }
                )
                ProgressBar(
                    modifier = Modifier
                        .constrainAs(progressBar) {
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                            start.linkTo(startTime.end, margin = Dp_12)
                            end.linkTo(endTime.start, margin = Dp_12)
                            width = Dimension.fillToConstraints
                        }
                        .height(Dp_5),
                    progress = progress,
                    isDragging = isDragging,
                    onValueChange = { viewModel.sendEvent(AudioTranscriptionEvent.SeekTo(it)) },
                    onDraggingChange = { isDragging ->
                        viewModel.isDragging.value = isDragging
                    }
                )

                Text(
                    text = formattedDuration,
                    color = Color.White.copy(alpha = 0.4f),
                    style = TextStyle(
                        fontSize = Sp_13,
                        fontWeight = FontWeight.W400,
                        textAlign = TextAlign.Start,
                        fontFamily = FontFamily.Serif // 使用等宽字体
                    ),
                    modifier = Modifier.constrainAs(endTime) {
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        end.linkTo(parent.end)
                    }
                )
            }
            PlaybackSpeedMenu(viewModel = viewModel, dialogState = dialogState)
        }
        Row(
            modifier = Modifier
                .padding(horizontal = Dp_20)
                .constrainAs(rowSec) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(rowFir.bottom, margin = Dp_10)
                    bottom.linkTo(parent.bottom)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            ConstraintLayout(
                modifier = Modifier.fillMaxWidth()
            ) {
                val (rewindBtn, playBtn, fastBtn, speedText) = createRefs()
                ReWindButton(
                    painter = painterResource(id = R.drawable.ic_rewind_button),
                    modifier = Modifier
                        .constrainAs(rewindBtn) {
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                            end.linkTo(playBtn.start, margin = Dp_34)
                        }
                        .size(Dp_28),
                    onClick = onRewind
                )

                PlayPauseButton(
                    painter = painterResource(
                        id = if (state.isPlaying && !isDragging) {
                            R.drawable.icon_normal_pause
                        } else {
                            R.drawable.icon_normal_play
                        }
                    ),
                    modifier = Modifier
                        .constrainAs(playBtn) {
                            centerHorizontallyTo(parent)
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                        }
                        .size(Dp_54, Dp_54),
                    onClick = onPlayPause
                )

                FastButton(
                    painter = painterResource(id = R.drawable.ic_fast_button),
                    modifier = Modifier
                        .constrainAs(fastBtn) {
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                            start.linkTo(playBtn.end, margin = Dp_34)
                        }
                        .size(Dp_28),
                    onClick = onFast
                )

                // 倍速
                Text(
                    text = "x${currentSpeed.value}",
                    style = TextStyle(
                        fontSize = Sp_15,
                        fontWeight = FontWeight.W400,
                        color = Color.Gray
                    ),
                    modifier = Modifier.constrainAs(speedText) {
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        end.linkTo(parent.end, margin = Dp_20)
                    }
                        .clickable {
                            viewModel.showSpeedMenuDialog(true)
                        }
                )
            }
        }
    }
}

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
private fun MenuItemPopup(
    viewModel: AudioTranscriptionViewModel,
    visible: MutableState<Boolean>,
    onDismiss: () -> Unit,
    onReTranscribeClick: () -> Unit,
    onReSummarizeClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    if (visible.value) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(Dp_16),
            contentAlignment = Alignment.TopEnd
        ) {
            AnimatedSheetBg(
                visible = visible.value,
                sheetBackgroundColor = ColorBlack50,
                onDismiss = onDismiss
            )

            Column(
                modifier = Modifier
                    .width(175.dp)
                    .wrapContentHeight()
                    .clip(RoundedCornerShape(Dp_16))
                    .background(color = Color18191A, shape = RoundedCornerShape(Dp_16)),
                horizontalAlignment = Alignment.Start
            ) {
                Spacer(modifier = Modifier.height(Dp_6))
                if (viewModel.isShowReOption()) {
                    if (viewModel.mState.value.audioBean?.isDistinguishSpeakers == true) {
                        ButtonItem(
                            text = stringResource(R.string.text_edit_speaker_name),
                            enable = !viewModel.isReTranscription,
                            onClick = {
                                viewModel.showQuickFixAllDialog(true)
                                onDismiss.invoke()
                            }
                        )
                    }
                    ButtonItem(
                        text = stringResource(R.string.text_re_transcribe),
                        enable = !viewModel.isReTranscription,
                        onClick = {
                            onReTranscribeClick.invoke()
                            onDismiss.invoke()
                        }
                    )
                    ButtonItem(
                        text = stringResource(R.string.text_re_summarize),
                        enable = !viewModel.isReSummary,
                        onClick = {
                            onReSummarizeClick.invoke()
                            onDismiss.invoke()
                        }
                    )
                }
                ButtonItem(
                    text = stringResource(R.string.libs_delete),
                    onClick = {
                        val recordResult = viewModel.recordResultLiveData.value
                        if (recordResult is RecordOptionResult.LoadingOption) {
                            if (recordResult.transcribeLoading) {
                                LibBaseApplication.instance.toast(R.string.record_wait_transcribe_finish)
                            } else if (recordResult.summaryLoading) {
                                LibBaseApplication.instance.toast(R.string.record_summarying_need_wait)
                            }
                            onDismiss.invoke()
                            return@ButtonItem
                        }
                        onDeleteClick.invoke()
                        onDismiss.invoke()
                    }
                )
                Spacer(modifier = Modifier.height(Dp_6))
            }
        }
    }
}

@Composable
private fun MenuShareItemPopup(
    viewModel: AudioTranscriptionViewModel,
    visible: MutableState<Boolean>,
    onDismiss: () -> Unit,
    buttonConfig: ButtonConfig.OneButton,
    onShareClick: (AudioTranscriptionEvent) -> Unit
) {
    val context = LocalContext.current
    val tab by viewModel.tabLiveData
    BottomSheet(visible = visible.value, onDismiss = onDismiss) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Min)
                .padding(horizontal = Dp_28, vertical = Dp_22)
        ) {
            ShareOptionRow(
                shareTitleResId = R.string.text_share_link,
                shareIconResId = R.mipmap.ic_share_link,
                shareOption = AudioTranscriptionEvent.ShareItem(
                    context = context,
                    shareType = AudioTranscriptionShareType.LINK
                ),
                onShareClick = {
                    // 这里会跳转子dialog, 不是实际分享
                    onDismiss.invoke()
                    viewModel.showShareLinkMenu(true)
                }
            )

            Spacer(modifier = Modifier.height(Dp_20))
            Divider(color = colorResource(R.color.white_10))
            Spacer(modifier = Modifier.height(Dp_20))

            ShareOptionRow(
                shareTitleResId = R.string.text_share_audio,
                shareIconResId = R.mipmap.ic_share_audio,
                shareOption = AudioTranscriptionEvent.ShareItem(
                    context = context,
                    shareType = AudioTranscriptionShareType.AUDIO
                ),
                onShareClick = { audioTranscriptionEvent ->
                    onShareClick(audioTranscriptionEvent)
                    onDismiss.invoke()
                }
            )

            if (viewModel.isShowReOption()) {
                Spacer(modifier = Modifier.height(Dp_20))
                Divider(color = colorResource(R.color.white_10))
                Spacer(modifier = Modifier.height(Dp_20))

                ShareOptionRow(
                    shareTitleResId = R.string.text_copy_transcribed,
                    shareIconResId = R.mipmap.ic_share_transcribed,
                    visible = !viewModel.isReTranscription && viewModel.transcribePhrases.isNotNullOrEmpty(),
                    shareOption = AudioTranscriptionEvent.CopyTranscribed,
                    onShareClick = { audioTranscriptionEvent ->
                        onShareClick(audioTranscriptionEvent)
                        onDismiss.invoke()
                    }
                )
                ShareOptionRow(
                    shareTitleResId = R.string.text_copy_summarize,
                    shareIconResId = R.mipmap.ic_share_summary,
                    shareOption = AudioTranscriptionEvent.CopySummary,
                    visible = !viewModel.isReSummary && viewModel.transcribeSummary.value.isNotNullOrEmpty(),
                    onShareClick = { audioTranscriptionEvent ->
                        onShareClick(audioTranscriptionEvent)
                        onDismiss.invoke()
                    }
                )

                Spacer(modifier = Modifier.height(Dp_20))
                Divider(color = colorResource(R.color.white_10))
                Spacer(modifier = Modifier.height(Dp_20))

                when (tab) {
                    Tab.Transcribe -> {
                        ShareOptionRow(
                            shareTitleResId = R.string.text_share_img_of_transcribed,
                            shareIconResId = R.mipmap.ic_share_img_of_transcribed,
                            visible = !viewModel.isReTranscription && viewModel.transcribePhrases.isNotNullOrEmpty(),
                            shareOption = AudioTranscriptionEvent.ShareItem(
                                context = context,
                                shareType = AudioTranscriptionShareType.IMAGE_OF_TRANSCRIBED
                            ),
                            onShareClick = { audioTranscriptionEvent ->
                                onShareClick(audioTranscriptionEvent)
                                onDismiss.invoke()
                                viewModel.setLoading(
                                    isLoading = true,
                                    type = LoadingType.IMAGE,
                                    desc = context.getString(R.string.text_share_image_preparing_tip)
                                )
                            }
                        )
                    }

                    Tab.Summary -> {
                        ShareOptionRow(
                            shareTitleResId = R.string.text_share_img_of_summary,
                            shareIconResId = R.mipmap.ic_share_img_of_summary,
                            visible = !viewModel.isReSummary && viewModel.transcribeSummary.value.isNotNullOrEmpty(),
                            shareOption = AudioTranscriptionEvent.ShareItem(
                                context = context,
                                shareType = AudioTranscriptionShareType.IMAGE_OF_SUMMARY
                            ),
                            onShareClick = { audioTranscriptionEvent ->
                                onShareClick(audioTranscriptionEvent)
                                onDismiss.invoke()
                                viewModel.setLoading(
                                    isLoading = true,
                                    type = LoadingType.IMAGE,
                                    desc = context.getString(R.string.text_share_image_preparing_tip)
                                )
                            }
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(Dp_20))
            // 取消按钮
            SubmitButton(
                subTitle = buttonConfig.button.text,
                enable = true,
                textColor = colorResource(R.color.white),
                enableColors = buttonConfig.button.enableColors,
                disableColors = buttonConfig.button.disableColors,
                modifier = Modifier
                    .fillMaxWidth()
                    // 28+2的padding
                    .padding(horizontal = Dp_2)
            ) {
                buttonConfig.button.onClick?.invoke()
                onDismiss()
            }
        }
    }
}

@Composable
private fun ShareOptionRow(
    shareTitleResId: Int,
    shareIconResId: Int,
    visible: Boolean = true,
    shareOption: AudioTranscriptionEvent,
    onShareClick: (AudioTranscriptionEvent) -> Unit
) {
    if (!visible) return
    val defaultFontWeight = 330
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(Dp_58)
            .clickable { onShareClick(shareOption) },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = stringResource(shareTitleResId),
            style = TextStyle(
                fontSize = Sp_16,
                color = colorResource(R.color.white),
                fontWeight = FontWeight(weight = defaultFontWeight)
            ),
            modifier = Modifier
                .wrapContentWidth()
                .height(Dp_21)
        )
        Image(
            painter = painterResource(shareIconResId),
            contentDescription = stringResource(shareTitleResId),
            modifier = Modifier
                .size(Dp_22)
        )
    }
}

@Composable
private fun MenuShareLinkPopup(
    viewModel: AudioTranscriptionViewModel,
    visible: MutableState<Boolean>,
    onDismiss: () -> Unit,
    buttonConfig: ButtonConfig.ThreeButton
) {
    val context = LocalContext.current
    BottomSheet(visible = visible.value, onDismiss = onDismiss) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Min)
                .padding(horizontal = Dp_28, vertical = Dp_22)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
                contentAlignment = Alignment.Center // 内容居中对齐
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    var isSummaryChecked by remember { mutableStateOf(false) }
                    var isTranscribedChecked by remember { mutableStateOf(false) }
                    var isAudioChecked by remember { mutableStateOf(false) }
                    // 标题 + 描述

                    fun isAllItemsNotChecked(): Boolean {
                        return !isSummaryChecked && !isTranscribedChecked && !isAudioChecked
                    }

                    Text(
                        text = stringResource(R.string.text_share_link),
                        style = TextStyle(
                            fontSize = Sp_16,
                            color = colorResource(R.color.white),
                            fontWeight = FontWeight(weight = 380)
                        ),
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = stringResource(R.string.text_share_link_desc),
                        style = TextStyle(
                            fontSize = Sp_14,
                            color = colorResource(R.color.white_60),
                            fontWeight = FontWeight(weight = 330)
                        ),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(Dp_20))
                    ShareItemRow(
                        shareTitleResId = R.string.text_item_summary,
                        visible = viewModel.transcribeSummary.value.isNotNullOrEmpty(),
                        isChecked = isSummaryChecked,
                        onItemClick = { checked ->
                            isSummaryChecked = checked
                        }
                    )
                    ShareItemRow(
                        shareTitleResId = R.string.text_item_transcribed,
                        visible = viewModel.transcribePhrasesList.isNotNullOrEmpty(),
                        isChecked = isTranscribedChecked,
                        onItemClick = { checked ->
                            isTranscribedChecked = checked
                        }
                    )
                    ShareItemRow(
                        shareTitleResId = R.string.text_share_audio,
                        isChecked = isAudioChecked,
                        onItemClick = { checked ->
                            isAudioChecked = checked
                        }
                    )

                    Spacer(modifier = Modifier.height(Dp_20))
                    // 分享按钮
                    SubmitButton(
                        subTitle = buttonConfig.button1.text,
                        enable = !isAllItemsNotChecked(),
                        textColor = colorResource(R.color.white),
                        enableColors = buttonConfig.button1.enableColors,
                        disableColors = buttonConfig.button1.disableColors,
                        modifier = Modifier
                            .fillMaxWidth()
                            // 28+2的padding
                            .padding(horizontal = Dp_2)
                    ) {
                        onDismiss()
                        viewModel.apply {
                            if (isAudioChecked && !containsFileId()) {
                                context.toast(R.string.text_fileId_is_missing)
                                return@apply
                            }
                            setLoading(
                                isLoading = true,
                                type = LoadingType.LINK,
                                desc = context.getString(R.string.text_link_generating)
                            )
                            sendEvent(
                                AudioTranscriptionEvent.ShareLink(
                                    shareOptions = Triple(
                                        isSummaryChecked,
                                        isTranscribedChecked,
                                        isAudioChecked
                                    ),
                                    textOnly = false
                                )
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(Dp_9))
                    // 复制链接按钮
                    SubmitButton(
                        subTitle = buttonConfig.button2.text,
                        enable = !isAllItemsNotChecked(),
                        textColor = colorResource(R.color.white),
                        enableColors = buttonConfig.button2.enableColors,
                        disableColors = buttonConfig.button2.disableColors,
                        modifier = Modifier
                            .fillMaxWidth()
                            // 28+2的padding
                            .padding(horizontal = Dp_2)
                    ) {
                        onDismiss()
                        viewModel.apply {
                            if (isAudioChecked && !containsFileId()) {
                                context.toast(R.string.text_fileId_is_missing)
                                return@apply
                            }
                            setLoading(
                                isLoading = true,
                                type = LoadingType.LINK,
                                desc = context.getString(R.string.text_link_generating)
                            )
                            sendEvent(
                                AudioTranscriptionEvent.ShareLink(
                                    shareOptions = Triple(
                                        isSummaryChecked,
                                        isTranscribedChecked,
                                        isAudioChecked
                                    ),
                                    textOnly = true
                                )
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(Dp_9))
                    // 取消按钮
                    SubmitButton(
                        subTitle = buttonConfig.button3.text,
                        enable = true,
                        textColor = colorResource(R.color.white),
                        enableColors = buttonConfig.button3.enableColors,
                        disableColors = buttonConfig.button3.disableColors,
                        modifier = Modifier
                            .fillMaxWidth()
                            // 28+2的padding
                            .padding(horizontal = Dp_2)
                    ) {
                        onDismiss()
                    }
                }
            }
        }
    }
}

@Composable
private fun ShareItemRow(
    shareTitleResId: Int,
    enabledIconResId: Int = R.mipmap.ic_share_item_checked,
    disabledIconResId: Int = R.mipmap.ic_share_item_unchecked,
    visible: Boolean = true,
    isChecked: Boolean,
    onItemClick: (Boolean) -> Unit
) {
    if (!visible) return
    val defaultFontWeight = 330
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(Dp_58)
            .clickable { onItemClick(!isChecked) },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = stringResource(shareTitleResId),
            style = TextStyle(
                fontSize = Sp_16,
                color = colorResource(R.color.white),
                fontWeight = FontWeight(weight = defaultFontWeight)
            ),
            modifier = Modifier
                .wrapContentWidth()
                .height(Dp_21)
        )
        Image(
            painter = painterResource(if (isChecked) enabledIconResId else disabledIconResId),
            contentDescription = stringResource(shareTitleResId),
            modifier = Modifier
                .size(Dp_22)
        )
    }
}

@Composable
private fun ButtonItem(
    text: String,
    enable: Boolean = true,
    onClick: () -> Unit
) {
    // 创建 InteractionSource 用于监听按压状态
    val interactionSource = remember { MutableInteractionSource() }
    // 监听按压状态
    val isPressed by interactionSource.collectIsPressedAsState()
    val backgroundColor = if (isPressed) Color222425 else Color18191A
    Box(
        modifier = Modifier
            .clickable(
                interactionSource = interactionSource,
                indication = rememberRipple(),
                onClick = { if (enable) onClick.invoke() }
            )
            .background(color = backgroundColor)
            .padding(horizontal = Dp_18, vertical = Dp_12)
            .fillMaxWidth()
    ) {
        Text(
            text = text,
            fontSize = Sp_14,
            fontWeight = FontWeight.W500,
            color = if (enable) Color.White else Color.Gray
        )
    }
}

@SuppressLint("SetJavaScriptEnabled")
@Composable
private fun ConstraintLayoutScope.WebViewWithLifecycle(
    topRef: ConstrainedLayoutReference,
    webContent: ConstrainedLayoutReference,
    bottomBtn: ConstrainedLayoutReference,
    viewModel: AudioTranscriptionViewModel,
    content: String
) {
    val scrollState = remember { ScrollState(0) }

    ConstraintLayout(
        modifier = Modifier
            .verticalScroll(scrollState)
            .constrainAs(webContent) {
                top.linkTo(topRef.bottom)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                bottom.linkTo(bottomBtn.top, margin = Dp_20)
                height = Dimension.fillToConstraints
            }
    ) {
        val (loading) = createRefs()
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
        ) {
            val context = LocalContext.current
            val lifecycleOwner = LocalLifecycleOwner.current
            // 使用Map存储WebView实例，避免重复创建
            val webViewMap = remember { mutableMapOf<String, WebView>() }
            val webViewKey = "webViewKey"

            // 状态管理
            val webViewState = remember { viewModel.webViewState }
            val (webView, isInitialized) = webViewState.value ?: run {
                val newWebView = webViewMap.getOrPut(webViewKey) {
                    WebView(context).apply {
                        settings.javaScriptEnabled = true
                        setBackgroundColor(0x00000000)
                    }
                }
                AudioTranscriptionViewModel.WebViewState(newWebView, false).also {
                    webViewState.value = it
                }
            }

            val webPageHelper = remember(webView) {
                WebPageHelper(context, webView).apply {
                    if (!isInitialized) {
                        initWebView(
                            true,
                            object : WebAppInterfaceListener() {
                                override fun getAllRecords(): String {
                                    val hasSummary = content.isNotEmpty()
                                    Timber.d("WebViewWithLifecycle webView init finish $hasSummary")
                                    if (hasSummary) {
                                        viewModel.showWebLoading.value = false
                                    }
                                    return content
                                }
                            }
                        ) {
                            recordChanged(
                                StreamType.SUMMARY_STREAM,
                                viewModel.generateSummaryRequestJson(),
                                System.currentTimeMillis().toString()
                            )
                        }
                        webViewState.value = webViewState.value?.copy(isInitialized = true)
                    }
                }.also { viewModel.setWebPageHelper(it) }
            }

            val isPageLoaded by webPageHelper.isPageLoaded

            // 处理内容更新（自动等待加载完成）
            LaunchedEffect(content, isPageLoaded) {
                Timber.d("WebViewWithLifecycle isPageLoaded:$isPageLoaded content:${content.isNotEmpty()}")
                if (content.isEmpty()) {
                    return@LaunchedEffect
                }
                Timber.d("WebViewWithLifecycle isInitialized:$isInitialized")
                if (isInitialized && content.isNotEmpty()) {
                    // web 初始化完成 isInitialized 在调用getData时会isInitialized = true
                    viewModel.showWebLoading.value = false
                    webPageHelper.recordChanged(
                        StreamType.SUMMARY_STREAM,
                        content,
                        System.currentTimeMillis().toString()
                    )
                } else {
                    webPageHelper.pendingRecords.add(content)
                }
            }

            // 生命周期管理
            DisposableEffect(lifecycleOwner) {
                val observer = LifecycleEventObserver { _, event ->
                    when (event) {
                        Lifecycle.Event.ON_PAUSE -> webView.onPause()
                        Lifecycle.Event.ON_RESUME -> webView.onResume()
                        Lifecycle.Event.ON_DESTROY -> {
                            viewModel.setWebPageHelper(null)
                            webView.removeAllViews()
                            webView.destroy()
                            webViewState.value = null
                        }

                        else -> {}
                    }
                }
                lifecycleOwner.lifecycle.addObserver(observer)
                onDispose {
                    lifecycleOwner.lifecycle.removeObserver(observer)
                }
            }
            Timber.d("load web view init:$isInitialized, parentIsNull:${webView.parent == null}")
            AndroidView(
                factory = {
                    if (webView.parent != null) {
                        Timber.d("WebView must be deleted, otherwise it will crash")
                        (webView.parent as ViewGroup).removeView(webView)
                    }
                    webView
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
            )
        }

        val showLoading = viewModel.showWebLoading.value
        Timber.d("WebViewWithLifecycle called showLoading:$showLoading")
        if (showLoading) {
            Column(
                modifier = Modifier
                    .constrainAs(loading) {
                        start.linkTo(parent.start)
                        top.linkTo(parent.top)
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom)
                    },
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val composition by rememberLottieComposition(
                    LottieCompositionSpec.Asset("loading.json")
                )

                LottieAnimation(
                    composition,
                    iterations = LottieConstants.IterateForever,
                    modifier = Modifier
                        .size(Dp_70)
                        .align(alignment = Alignment.CenterHorizontally)
                )

                Text(
                    text = stringResource(R.string.loading),
                    fontSize = Sp_14,
                    fontWeight = FontWeight.W400,
                    color = Color.White.copy(alpha = 0.4f),
                    modifier = Modifier
                        .wrapContentWidth()
                        .wrapContentHeight()
                        .align(alignment = Alignment.CenterHorizontally)
                )
            }
        }
    }
}

@Composable
private fun ConstraintLayoutScope.TransliterateFail(
    viewModel: AudioTranscriptionViewModel,
    transOrSummaryFail: RecordSummaryFail?,
    topRef: ConstrainedLayoutReference,
    bottomRef: ConstrainedLayoutReference,
    transcriptionFail: ConstrainedLayoutReference,
    uploadFailed: Boolean = false
) {
    Timber.d("TransliterateFail called => $transOrSummaryFail")
    transOrSummaryFail?.let {
        val title = when (it) {
            is RecordSummaryFail.TranslateFail -> {
                // 转写错误
                LibBaseApplication.instance.getString(
                    if (uploadFailed) R.string.text_upload_failed else R.string.text_generate_fail
                )
            }

            is RecordSummaryFail.SummaryFail -> {
                LibBaseApplication.instance.getString(R.string.text_generate_ai_summary_fail)
            }
        }

        val pair = desc(it.code)
        val reason = if (uploadFailed) {
            stringResource(R.string.text_upload_err_code_system, it.code)
        } else {
            pair.first
        }
        val showRetry = pair.second

        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .background(ColorBlack)
                .constrainAs(transcriptionFail) {
                    top.linkTo(topRef.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(bottomRef.top)
                    height = Dimension.fillToConstraints
                }
        ) {
            val (iconRef, titleRef, descRef, buttonRef) = createRefs()
            Image(
                painter = painterResource(id = R.drawable.ic_transcription_fail),
                contentDescription = "empty_tips",
                modifier = Modifier
                    .constrainAs(iconRef) {
                        top.linkTo(parent.top, margin = Dp_150)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .size(Dp_80)
            )

            Text(
                text = title,
                fontSize = Sp_16,
                fontWeight = FontWeight.W500,
                color = ColorWhite,
                modifier = Modifier.constrainAs(titleRef) {
                    top.linkTo(iconRef.bottom, margin = Dp_12)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )

            Text(
                text = reason,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                color = Color.White.copy(alpha = 0.4f),
                modifier = Modifier.constrainAs(descRef) {
                    top.linkTo(titleRef.bottom, margin = Dp_12)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )

            if (showRetry) {
                SubmitButton(
                    subTitle = stringResource(R.string.text_re_generate),
                    enable = true,
                    enableColors = listOf(Color222425, Color222425),
                    disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
                    textColor = Color.White,
                    modifier = Modifier
                        .constrainAs(buttonRef) {
                            top.linkTo(descRef.bottom, margin = Dp_40)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .size(Dp_155, Dp_46)
                ) {
                    if (checkNetState()) {
                        viewModel.reTranscription()
                        if (it is RecordSummaryFail.TranslateFail) {
                            if (viewModel.isCanShowTab()) {
                                viewModel.sendEvent(AudioTranscriptionEvent.ReTranscribe)
                            } else {
                                viewModel.sendEvent(AudioTranscriptionEvent.RequestTranscribe)
                            }
                        } else if (it is RecordSummaryFail.SummaryFail) {
                            viewModel.sendEvent(AudioTranscriptionEvent.RequestSummary(viewModel.summaryTemplate))
                        }
                    }
                }
            }
        }
    }
}

/**
 * 根据错误码来返回展示失败的原因
 * @return pair.first 是失败原因，pair.second是否展示重试按钮
 */
private fun desc(code: Int): Pair<String, Boolean> {
    val context = LibBaseApplication.instance
    return when (code) {
        ERROR_CODE_EMPTY_1 -> {
            Pair(
                context.getString(ErrorCode.NET_WORK_ERROR.reason),
                ErrorCode.NET_WORK_ERROR.showRetry
            )
        }

        ERROR_CODE_EMPTY_AUDIO -> {
            Pair(context.getString(ErrorCode.TRANS_EMPTY.reason), ErrorCode.TRANS_EMPTY.showRetry)
        }

        ERROR_CODE_SENSITIVE_411,
        ERROR_CODE_SENSITIVE_421 -> {
            Pair(context.getString(ErrorCode.SENSITIVE.reason), ErrorCode.SENSITIVE.showRetry)
        }

        ERROR_CODE_SUMMARY_FAIL_443 -> {
            Pair(context.getString(ErrorCode.SUMMARY_FAIL.reason), ErrorCode.SUMMARY_FAIL.showRetry)
        }

        else -> {
            Pair(
                context.getString(ErrorCode.SYSTEM.reason, "$code"),
                ErrorCode.SYSTEM.showRetry
            )
        }
    }
}

enum class ErrorCode(val reason: Int, val showRetry: Boolean) {
    NET_WORK_ERROR(R.string.text_trans_err_code_net_error, true),
    TRANS_EMPTY(R.string.text_trans_err_code_empty, true),
    SENSITIVE(R.string.text_trans_err_code_sensitive, false),
    SUMMARY_FAIL(R.string.text_summary_err_code_word_low, false),
    SYSTEM(R.string.text_trans_err_code_system, true)
}

private const val ERROR_CODE_EMPTY_1 = -1
private const val ERROR_CODE_EMPTY_AUDIO = 204
private const val ERROR_CODE_SENSITIVE_411 = 411
private const val ERROR_CODE_SENSITIVE_421 = 421
private const val ERROR_CODE_SUMMARY_FAIL_443 = 443
