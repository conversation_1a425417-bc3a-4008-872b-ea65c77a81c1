@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Long<PERSON><PERSON>meterL<PERSON>")

package com.superhexa.supervision.feature.miwear.speechhub.compont

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.RecordStatus
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.PlayState
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.RecordListEvent
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.RecordingListViewModel
import com.superhexa.supervision.library.base.basecommon.compose.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.Color1AFF0050
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4_10
import com.superhexa.supervision.library.base.basecommon.theme.ColorFF0050
import com.superhexa.supervision.library.base.basecommon.theme.ColorFF0050_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_15
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_24
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_3
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.theme.Dp_35
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_6
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_82
import com.superhexa.supervision.library.base.basecommon.theme.Sp_12
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.db.bean.AudioTranscriptionBean
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.statistic.O95Statistic
import timber.log.Timber

@Composable
fun RecordItem(
    item: MediaBean,
    audioItem: AudioTranscriptionBean,
    viewModel: RecordingListViewModel,
    editAction: (MediaBean) -> Unit,
    deleteAction: (MediaBean) -> Unit,
    onValueChange: (Float) -> Unit,
    onClick: () -> Unit
) {
    val state by viewModel.mState.collectAsState()
    val processStatusMap by viewModel.processStatusFlow.collectAsState()
    val processStatus = processStatusMap[item.path]

    val isCurrentPlaying =
        (state.currentPlayingPath == item.path) &&
            (
                (state.playStatus == PlayState.PLAY) ||
                    (state.playStatus == PlayState.PAUSE)
                )
    var isDragging by remember { mutableStateOf(false) }
    val progress = if (isCurrentPlaying) state.playbackProgress else 0L
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = Color18191A, // 背景颜色
                shape = RoundedCornerShape(Dp_16) // 圆角形状
            )
            .clickable {
                onClick.invoke()
            }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = Dp_20, top = Dp_20, end = Dp_16),
            horizontalArrangement = Arrangement.Start
        ) {
            PlayPauseButton(
                painter = painterResource(id = if (isCurrentPlaying && state.isPlaying && !isDragging) R.drawable.icon_pause else R.drawable.icon_play),
                modifier = Modifier
                    .size(Dp_32, Dp_35)
                    .padding(top = Dp_5),
                onClick = {
                    if (item.path != state.currentPlayingPath) {
                        Timber.d("switch other audio")
                        viewModel.resetPlayStatus()
                    }

                    viewModel.sendEvent(RecordListEvent.PlayOrPause(item, audioItem))
                    O95Statistic.clickRecordOption("play_button")
                }
            )
            Spacer(modifier = Modifier.width(Dp_14))
            FileInfoRow(
                fileName = item.fileName,
                duration = item.duration,
                fileAdded = item.fileAdded,
                showRedDot = audioItem.isFirstShow,
                processStatus = processStatus
            )
        }
        if (isCurrentPlaying) {
            val audioDuration = item.duration * 1000 // ms
            ProgressBar(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Dp_18)
                    .padding(start = Dp_20, top = Dp_14, end = Dp_20),
                progress = if (audioDuration > 0) {
                    (progress.toFloat() / audioDuration.toFloat())
                } else {
                    0f
                },
                isDragging = isDragging,
                onValueChange = onValueChange,
                onDraggingChange = {
                    isDragging = it
                    Timber.d("isDragging: $isDragging")
                }
            )
            Spacer(modifier = Modifier.width(Dp_8))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = Dp_20, top = Dp_8, end = Dp_20),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = DateTimeUtils.videoDuration(progress),
                    color = if (isDragging) Color.White else Color.White.copy(alpha = 0.4f),
                    fontSize = Sp_12,
                    fontWeight = FontWeight.W400
                )
                Text(
                    text = DateTimeUtils.videoDuration(audioDuration),
                    color = Color.White.copy(alpha = 0.4f),
                    fontSize = Sp_12,
                    fontWeight = FontWeight.W400
                )
            }
        }
        // Divider
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_15)
                .padding(start = Dp_20, top = Dp_14, end = Dp_20)
                .background(Color222425)
        )

        ActionButtons(
            editAction = {
                viewModel.curMediaBean.value = item
                editAction.invoke(item)
            },
            deleteAction = {
                viewModel.curMediaBean.value = item
                deleteAction.invoke(item)
            }
        )
        Spacer(modifier = Modifier.height(Dp_20))
    }
}

@Composable
fun PlayPauseButton(
    painter: Painter,
    modifier: Modifier,
    onClick: () -> Unit
) {
    Image(
        painter = painter,
        contentDescription = "Play/Pause",
        modifier = modifier
            .clickable(onClick = onClick)
    )
}

// 快退
@Composable
fun ReWindButton(
    painter: Painter,
    modifier: Modifier,
    onClick: () -> Unit
) {
    Image(
        painter = painter,
        contentDescription = "Rewind",
        modifier = modifier
            .clickable(onClick = onClick)
    )
}

// 快进
@Composable
fun FastButton(
    painter: Painter,
    modifier: Modifier,
    onClick: () -> Unit
) {
    Image(
        painter = painter,
        contentDescription = "Fast",
        modifier = modifier
            .clickable(onClick = onClick)
    )
}

@Composable
fun FileInfoRow(
    fileName: String,
    duration: Long,
    fileAdded: Long,
    showRedDot: Boolean, // 控制是否显示红色小圆点
    processStatus: String? = null // 新增录音处理状态参数
) {
    Column {
        Row(modifier = Modifier.wrapContentWidth()) {
            Text(
                text = fileName.ifEmpty { DateTimeUtils.convertTimeStampToString(fileAdded) },
                color = Color.White,
                fontSize = Sp_16,
                overflow = TextOverflow.Ellipsis,
                fontWeight = FontWeight.W500,
                maxLines = 1,
                modifier = Modifier.padding(end = Dp_4)
            )

            Spacer(modifier = Modifier.width(Dp_4))

            // 红色小圆点
            if (showRedDot) {
                Canvas(
                    modifier = Modifier
                        .size(Dp_6)
                ) {
                    drawCircle(color = Color.Red, radius = Dp_3.toPx())
                }
            }
        }

        Spacer(modifier = Modifier.height(Dp_4))

        // 修改这个Row，使其支持录音状态的右对齐显示
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 左侧：录音时长和录音时间
            Row {
                Text(
                    text = DateTimeUtils.formatDuration(duration * 1000),
                    color = Color.White.copy(alpha = 0.6f),
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400
                )
                Spacer(modifier = Modifier.width(Dp_8))
                Box(
                    Modifier
                        .width(Dp_1)
                        .height(Dp_12)
                        .background(color = Color222425)
                        .align(Alignment.CenterVertically)
                )
                Spacer(modifier = Modifier.width(Dp_8))
                Text(
                    text = DateTimeUtils.convertTimeStampToDateString(fileAdded),
                    color = Color.White.copy(alpha = 0.6f),
                    fontSize = Sp_13,
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = FontWeight.W400
                )
            }

            // 右侧：录音状态
            // 注意：由于父Row已有end=Dp_16的padding，这里不需要额外padding
            // 最终效果：录音状态距离卡片右边缘16dp
            ProcessStatusIndicator(processStatus)
        }
    }
}

@Composable
fun ProgressBar(
    modifier: Modifier = Modifier,
    progress: Float,
    isDragging: Boolean,
    onValueChange: (Float) -> Unit,
    onDraggingChange: (Boolean) -> Unit
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(Dp_4))
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { onDraggingChange.invoke(true) },
                    onDragEnd = { onDraggingChange.invoke(false) },
                    onDrag = { change, _ ->
                        val newProgress = (change.position.x / size.width).coerceIn(0f, 1f)
                        onValueChange(newProgress)
                    }
                )
            }
    ) {
        LinearProgressIndicator(
            progress = progress,
            modifier = Modifier.matchParentSize(),
            color = if (isDragging) Color55D8E4 else Color.White,
            backgroundColor = Color.White.copy(alpha = 0.3f)
        )
    }
}

@Composable
fun ActionButtons(
    editAction: () -> Unit,
    deleteAction: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = Dp_20, top = Dp_14, end = Dp_20),
        horizontalArrangement = Arrangement.Start
    ) {
        // 操作按钮
        Image(
            painter = painterResource(id = R.drawable.icon_edit),
            contentDescription = "Edit",
            modifier = Modifier
                .size(Dp_24)
                .clickable(onClick = editAction)
        )
        Spacer(modifier = Modifier.width(Dp_28))
        Image(
            painter = painterResource(id = R.drawable.icon_delete),
            contentDescription = "Delete",
            modifier = Modifier
                .size(Dp_24)
                .clickable(onClick = deleteAction)
        )
    }
}

/**
 * 录音处理状态指示器
 */
@Composable
fun ProcessStatusIndicator(status: String?) {
    when (status) {
        "UPLOADING" -> {
            // 上传中 - 显示文字
            Text(
                text = "上传中",
                fontSize = Sp_12,
                fontWeight = FontWeight.W400,
                color = Color55D8E4,
                modifier = Modifier
                    .background(
                        color = Color55D8E4_10,
                        shape = RoundedCornerShape(Dp_4)
                    )
                    .padding(horizontal = Dp_4, vertical = Dp_2)
            )
        }
        "TRANSCRIBING" -> {
            // 转写中 - 显示文字
            Text(
                text = "正在生成",
                fontSize = Sp_12,
                fontWeight = FontWeight.W400,
                color = Color55D8E4,
                modifier = Modifier
                    .background(
                        color = Color55D8E4_10,
                        shape = RoundedCornerShape(Dp_4)
                    )
                    .padding(horizontal = Dp_4, vertical = Dp_2)
            )
        }
        "TRANSCRIPTION_COMPLETED" -> {
            // 转写完成 - 显示文字
            Text(
                text = "已生成",
                fontSize = Sp_12,
                fontWeight = FontWeight.W400,
                color = Color55D8E4,
                modifier = Modifier
                    .background(
                        color = Color55D8E4_10,
                        shape = RoundedCornerShape(Dp_4)
                    )
                    .padding(horizontal = Dp_4, vertical = Dp_2)
            )
        }
        "TRANSCRIPTION_FAILED" -> {
            // 转写失败 - 显示文字
            Text(
                text = "生成失败",
                fontSize = Sp_12,
                fontWeight = FontWeight.W400,
                color = ColorFF0050,
                modifier = Modifier
                    .background(
                        color = ColorFF0050_10,
                        shape = RoundedCornerShape(Dp_4)
                    )
                    .padding(horizontal = Dp_4, vertical = Dp_2)
            )
        }
        "SUMMARIZING" -> {
            // 总结中 - 显示文字
            Text(
                text = "正在生成",
                fontSize = Sp_12,
                fontWeight = FontWeight.W400,
                color = Color55D8E4,
                modifier = Modifier
                    .background(
                        color = Color55D8E4_10,
                        shape = RoundedCornerShape(Dp_4)
                    )
                    .padding(horizontal = Dp_4, vertical = Dp_2)
            )
        }
        "SUMMARY_COMPLETED" -> {
            // 总结完成 - 显示文字
            Text(
                text = "已生成",
                fontSize = Sp_12,
                fontWeight = FontWeight.W400,
                color = Color55D8E4,
                modifier = Modifier
                    .background(
                        color = Color55D8E4_10,
                        shape = RoundedCornerShape(Dp_4)
                    )
                    .padding(horizontal = Dp_4, vertical = Dp_2)
            )
        }
        "SUMMARY_FAILED" -> {
            // 总结失败 - 显示文字
            Text(
                text = "生成失败",
                fontSize = Sp_12,
                fontWeight = FontWeight.W400,
                color = ColorFF0050,
                modifier = Modifier
                    .background(
                        color = ColorFF0050_10,
                        shape = RoundedCornerShape(Dp_4)
                    )
                    .padding(horizontal = Dp_4, vertical = Dp_2)
            )
        }
        else -> {
            // 未处理或其他状态 - 不显示任何内容
            Spacer(modifier = Modifier.size(Dp_12))
        }
    }
}

@Composable
private fun TimerView(recordStatus: Int, text: String) {
    Text(
        text = text,
        fontSize = Sp_16,
        fontWeight = FontWeight.W500,
        color = when (recordStatus) {
            RecordStatus.RECORD_PAUSE -> colorResource(R.color.color_FF0050)
            else -> colorResource(R.color.white)
        },
        modifier = Modifier.padding(start = Dp_20)
    )
}

@Composable
fun EndRecording(recordStatus: Int, viewModel: RecordingListViewModel, onClick: () -> Unit) {
    val alphaAnimatable = remember { Animatable(0f) }
    val recordTime = viewModel.recordTimeText.collectAsState()
    LaunchedEffect(Unit) {
        alphaAnimatable.animateTo(
            targetValue = 1f,
            animationSpec = tween(durationMillis = 1000)
        )
    }
    val alpha by alphaAnimatable.asState()

    AnimatedVisibility(
        visible = (recordStatus == RecordStatus.RECORD_ING || recordStatus == RecordStatus.RECORD_PAUSE)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_82)
                .background(
                    color = Color18191A, // 背景颜色
                    shape = RoundedCornerShape(Dp_16) // 圆角形状
                )
                .alpha(alpha)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxHeight(), // 让 Column 占满整个父布局，才能居中
                verticalArrangement = Arrangement.Center // 垂直居中
            ) {
                TimerView(recordStatus, recordTime.value)
                Text(
                    text = when (recordStatus) {
                        RecordStatus.RECORD_PAUSE -> stringResource(R.string.text_record_pause)
                        else -> stringResource(R.string.text_recording)
                    },
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    color = colorResource(R.color.white_40),
                    modifier = Modifier.padding(start = Dp_20)
                )
            }

            Text(
                text = stringResource(R.string.text_end_recording),
                style = TextStyle(
                    color = ColorFF0050,
                    fontSize = Sp_13,
                    textAlign = TextAlign.Center
                ),
                modifier = Modifier
                    .align(Alignment.CenterEnd) // ✅ 垂直居中 + 靠右对齐
                    .padding(end = Dp_20) // 可选：添加一些右边距
                    .clip(RoundedCornerShape(Dp_16))
                    .background(Color1AFF0050.copy(alpha = 0.1f))
                    .align(Alignment.CenterEnd)
                    .padding(horizontal = Dp_18, vertical = Dp_10)
                    .clickDebounce {
                        onClick.invoke()
                    }
            )
        }
    }
}
