package com.xiaomi.aivs.engine.policy.timber

import android.os.Handler
import android.os.HandlerThread
import com.xiaomi.aivs.data.TimeOutPolicyNode
import com.xiaomi.aivs.engine.state.EngineStateMachine
import timber.log.Timber


class TimeoutTimer(
    @TimeOutPolicyNode private val node: String,
    private val onTimeDone: () -> Unit
) : ITimeOut {
    // 使用主线程 Handler（如需后台线程，可替换为 HandlerThread 的 Looper）
    private val handlerThread = HandlerThread("TimeoutThread").apply { start() }
    private val handler = Handler(handlerThread.looper)
    private var timeoutRunnable: TimeoutRunnable? = null

    override fun isActive(): Boolean = timeoutRunnable != null

    override fun restartTimer(reason: String?, countdownTime: Long) {
        Timber.tag(node).d("restartTimer: $reason, countdown=${countdownTime}s")
        cancelTimer(reason) // 强制取消之前的任务
        wakeupTimer(reason, countdownTime)
    }

    @Synchronized
    private fun wakeupTimer(reason: String?, countdownTime: Long) {
        if (EngineStateMachine.isIdle()) {
            Timber.tag(node).w("非连续对话中，不启动倒计时")
            return
        }

        // 明确单位转换：秒 -> 毫秒
        val delayMillis = countdownTime * TIME_UNIT
        Timber.tag(node).d("wakeupTimer: $reason, $countdownTime,delay=${delayMillis}ms")

        val newRunnable = TimeoutRunnable(reason) {
            Timber.tag(node).d("Timer expired: $reason")
            onTimeDone.invoke()
            synchronized(this) { timeoutRunnable = null }
        }

        synchronized(this) {
            timeoutRunnable = newRunnable
            handler.postDelayed(newRunnable, delayMillis)
        }
    }

    @Synchronized
    override fun cancelTimer(reason: String?) {
        Timber.tag(node).d("cancelTimer attempt: $reason, current=${timeoutRunnable?.reason}")
        timeoutRunnable?.let {
            val cancelledReason = it.reason  // 先保存reason
            handler.removeCallbacks(it)
            timeoutRunnable = null
            Timber.tag(node).d("cancelTimer success: $reason, cancelled=$cancelledReason")
        }
    }

    override fun timerNode(): String? = if (isActive()) node else null

    fun release() {
        cancelTimer("release") // 清理资源
    }

    companion object {
        private const val TIME_UNIT = 1000L // 固定单位：秒转毫秒
    }
}