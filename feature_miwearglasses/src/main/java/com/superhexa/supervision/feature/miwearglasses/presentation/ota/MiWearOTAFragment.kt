package com.superhexa.supervision.feature.miwearglasses.presentation.ota

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.View
import androidx.activity.addCallback
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.Divider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95State
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.CameraJointDetectionManager
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.presentation.ota.component.UpdateProgress
import com.superhexa.supervision.feature.miwearglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.miwearglasses.presentation.space.dialogs.FileTransO95Callback
import com.superhexa.supervision.filetrans.handler.MediaSpaceHandler
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.miwearglasses_MiWearOTAFragment
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2ButtonCompat
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes3Button
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDesOneButton
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.GLASSES_DEVICE_OTA_STATUS
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FRAGMENT_FIRM_TAG
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color3AE1D2
import com.superhexa.supervision.library.base.basecommon.theme.Color3FD4FF
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite10
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_150
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_41
import com.superhexa.supervision.library.base.basecommon.theme.Dp_85
import com.superhexa.supervision.library.base.basecommon.theme.Sp_14
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_19
import com.superhexa.supervision.library.base.basecommon.theme.Sp_21
import com.superhexa.supervision.library.base.basecommon.theme.Sp_24
import com.superhexa.supervision.library.base.basecommon.theme.Sp_30
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import com.superhexa.supervision.library.statistic.O95Statistic
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2024/9/29 on 10:59
 * 作者: qintaiyuan
 */
@Suppress("TooManyFunctions")
@Route(path = miwearglasses_MiWearOTAFragment)
class MiWearOTAFragment : BaseComposeFragment() {
    private val viewModel by instance<MiWearOTAViewModel>()
    private val showNavIcon = mutableStateOf(true)
    private val showOTATips = mutableStateOf(false)
    private val showWifiConfigTips = mutableStateOf(false)
    private val showAppDownload = mutableStateOf(false)
    private val showWiFiDlg = mutableStateOf(false)
    private val showRecordingDlg = mutableStateOf(false)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            exit()
        }
        sendInitEvent()
        O95Statistic.exposeTip43025("Glasses_Settings_OTA")
    }

    private fun sendInitEvent() {
        val otaState = arguments?.getInt(GLASSES_DEVICE_OTA_STATUS) ?: 1
        showLoading()
        viewModel.sendEvent(
            MiWearOTAEvent.InitData(otaState) {
                hideLoading()
            }
        )
    }

    private fun exit() {
        if (showNavIcon.value) {
            navigator.pop()
        }
    }

    /**
     * 检查设备是否支持通过局域网（P2P/热点）推送OTA包
     */
    private fun isSupportLanOTA(): Boolean {
        return viewModel.deviceStateLiveData.value?.deviceCapability?.supportPushOTA ?: false
    }

    override val contentView: @Composable () -> Unit = {
        KeepScreenOn()
        val mState = viewModel.mState.collectAsState()
        LaunchedEffect(mState.value.deviceUpdateState) {
            when (val updateState = mState.value.deviceUpdateState) {
                is DeviceOTAState.LatestVersion,
                is DeviceOTAState.Update -> showNavIcon.value = true
                is DeviceOTAState.Downloaded -> {
                    showNavIcon.value = false
                    if (!NetWorkUtil.isWifiEnabled(requireActivity())) {
                        viewModel.sendEvent(MiWearOTAEvent.Reset)
                        showWiFiDlg.value = true
                    } else {
                        // 下载完成往下走
                        Timber.d("downloaded called start connect p2p")
                        startP2PConnect(updateState)
                    }
                }
                else -> showNavIcon.value = false
            }
        }
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, items, btn) = createRefs()
            FunList(
                otaStateState = mState,
                modifier = Modifier.constrainAs(items) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom, margin = Dp_85)
                    height = Dimension.fillToConstraints
                }
            )
            CommonTitleBar(
                "",
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) },
                backIcVisible = showNavIcon.value
            ) {
                exit()
            }
            UpdateProgress(
                modifier = Modifier
                    .constrainAs(btn) {
                        bottom.linkTo(parent.bottom)
                    }
                    .fillMaxWidth()
                    .padding(horizontal = Dp_28, vertical = Dp_28),
                deviceUpdateState = mState.value.deviceUpdateState
            ) { update ->
                O95Statistic.clickSettingOTAEvent()
                when (update) {
                    is DeviceOTAState.Update -> {
                        CameraJointDetectionManager.checkIsJointState {
                            checkWifiState()
                        }
                    }

                    is DeviceOTAState.LatestVersion -> checkDeviceUpdateState()

                    is DeviceOTAState.OTASuccess,
                    is DeviceOTAState.OTAFailed -> {
                        HexaRouter.Home.backToHome(this@MiWearOTAFragment)
                    }
                    else -> {}
                }
            }
        }
        LaunchedEffect(viewModel) {
            viewModel.mEffect.collect { effect ->
                handelEffect(effect)
            }
        }
        OTATipsDialog()
        OTALowBatteryDialog(mState)
        LowStorageTipsDialog(mState)
        DeviceBusyTipsDialog(mState)
        DeviceNetTipsDialog(mState)
        WiFiConfigTipsDialog()
        AppDownloadTipsDialog()
        OTALowTemperatureDialog(mState)
        OTAUpgradeFailDialog(mState)
        WiFiDialog()
        RecordingDialog(true)
    }

    private fun handelEffect(effect: MiWearOTAEffect) {
        when (effect) {
            is MiWearOTAEffect.DeviceDisconnected -> {
                toast(R.string.libs_ota_dis_connected_tip)
                PriorityDialogManager.dismissCurrentDialogByTag(FRAGMENT_FIRM_TAG)
                viewModel.sendEvent(MiWearOTAEvent.OnExit)
                HexaRouter.Home.backToHome(this)
            }
        }
    }

    private fun checkDeviceUpdateState() {
        showLoading()
        viewModel.sendEvent(
            MiWearOTAEvent.CheckUpdateState {
                hideLoading()
                if (it == null) {
                    toast(R.string.libs_latest_version)
                }
            }
        )
    }

    @Suppress("TooManyFunctions", "LongMethod")
    @Composable
    private fun FunList(
        otaStateState: State<MiWearOTAState>,
        modifier: Modifier
    ) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(Dp_12),
            modifier = modifier
                .fillMaxWidth()
                .fillMaxHeight() // 确保 LazyColumn 有足够的高度来滚动
        ) {
            item {
                ConstraintLayout(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    val (log, version, space, updateTip, updateDesc) = createRefs()
                    Text(
                        text = stringResource(id = R.string.libs_xiaomi_device_update),
                        Modifier.constrainAs(log) {
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            top.linkTo(parent.top, margin = Dp_150)
                        },
                        style = TextStyle(
                            color = ColorWhite,
                            fontSize = Sp_30,
                            fontWeight = FontWeight.W500
                        )
                    )
                    Version(
                        modifier = Modifier.constrainAs(version) {
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            top.linkTo(log.bottom, margin = Dp_20)
                        },
                        otaStateState = otaStateState
                    )
                    if (otaStateState.value.updateDesc.isNotNullOrEmpty()) {
                        LineSpace(
                            modifier = Modifier.constrainAs(space) {
                                top.linkTo(version.bottom, margin = Dp_100)
                            }
                        )

                        UpdateTip(
                            modifier = Modifier.constrainAs(updateTip) {
                                top.linkTo(space.bottom, margin = Dp_18)
                                start.linkTo(parent.start, margin = Dp_20)
                            }
                        )

                        UpdateDesc(
                            modifier = Modifier.constrainAs(updateDesc) {
                                top.linkTo(updateTip.bottom, margin = Dp_12)
                                start.linkTo(parent.start, margin = Dp_20)
                                end.linkTo(parent.end, margin = Dp_20)
                                width = Dimension.fillToConstraints
                            },
                            otaStateState = otaStateState
                        )
                    }
                }
            }
        }
    }

    @Composable
    private fun Logo(modifier: Modifier) {
        Image(
            painter = painterResource(id = R.drawable.ic_xiaomi_os_logo),
            contentDescription = "logo",
            modifier = modifier
        )
    }

    @Composable
    private fun Version(modifier: Modifier, otaStateState: State<MiWearOTAState>) {
        Text(
            text = if (otaStateState.value.deviceVersion.isNotNullOrEmpty()) {
                otaStateState.value.deviceVersion
            } else {
                ""
            },
            style = TextStyle(
                color = ColorWhite60,
                fontWeight = FontWeight.W300,
                fontSize = Sp_14,
                lineHeight = Sp_19
            ),
            modifier = modifier
        )
    }

    @Composable
    private fun deviceVersion(deviceState: State<O95State?>): String {
        val updateInfo = deviceState.value?.updateInfo
        return if (updateInfo != null) {
            "v${updateInfo.versionCode}"
        } else {
            "v${deviceState.value?.deviceInfo?.firmwareVersion ?: ""}"
        }
    }

    @Composable
    private fun LineSpace(modifier: Modifier) {
        Divider(
            color = ColorWhite10, // 设置线条颜色
            thickness = Dp_0_5, // 设置线条厚度
            modifier = modifier
                .fillMaxWidth() // 设置宽度为父容器的宽度
                .height(Dp_41)
                .padding(horizontal = Dp_20, vertical = Dp_20) // 设置两边间距
        )
    }

    @Composable
    private fun UpdateTip(modifier: Modifier) {
        Text(
            text = stringResource(id = R.string.libs_update_tip),
            style = TextStyle(
                color = ColorWhite,
                fontWeight = FontWeight.W500,
                fontSize = Sp_16,
                lineHeight = Sp_21
            ),
            modifier = modifier
        )
    }

    @Composable
    private fun UpdateDesc(modifier: Modifier, otaStateState: State<MiWearOTAState>) {
        Text(
            text = otaStateState.value.updateDesc,
            style = TextStyle(
                color = ColorWhite60,
                fontWeight = FontWeight.W400,
                fontSize = Sp_14,
                lineHeight = Sp_24
            ),
            modifier = modifier
        )
    }

    @Composable
    private fun KeepScreenOn() {
        val currentView = LocalView.current
        DisposableEffect(Unit) {
            currentView.keepScreenOn = true
            onDispose {
                currentView.keepScreenOn = false
            }
        }
    }

    private fun checkWifiState() {
        if (viewModel.isChannelSuccess()) {
            checkTransFileState {
                launch {
                    showLoading()
                    val wifiConfig = viewModel.getWifiConfig()
                    hideLoading()
                    if (wifiConfig?.list.isNullOrEmpty()) {
                        // 检测wifi
                        showWifiConfigTips.value = true
                    } else {
                        viewModel.otaType.value = OTAType.WIFI
                        showOTATips.value = true
                    }
                }
            }
        } else {
            toast(R.string.deviceNotConnectToOTA)
        }
    }

    private fun checkTransFileState(call: () -> Unit) {
        if (MediaSpaceHandler.isDownloading()) {
            Timber.d("startOTA---O95DownloadMediaFileService is running")
            toast(R.string.libs_ota_device_basy)
        } else {
            call.invoke()
        }
    }

    @Composable
    private fun WiFiConfigTipsDialog() {
        val supportLanOTA = isSupportLanOTA()
        Timber.d("WIFIConfigTipsDialog called >> $supportLanOTA")
        if (supportLanOTA) {
            // 支持局域网OTA，显示双按钮对话框
            BottomSheetTitleDes3Button(
                title = stringResource(id = R.string.libs_please_config_network),
                des = stringResource(id = R.string.libs_please_config_network_desc),
                visible = showWifiConfigTips.value,
                buttonConfig = ButtonConfig.ThreeButton(
                    ButtonParams(
                        text = stringResource(R.string.libs_ota_setting_wlan),
                        textColor = ColorBlack,
                        enableColors = listOf(Color3AE1D2, Color3FD4FF)
                    ) {
                        HexaRouter.Settinng.navigateToWlan(this)
                    },
                    ButtonParams(text = stringResource(id = R.string.libs_ota_through_phone)) {
                        // 用户点击通过手机升级
                        usePhoneUpgrade()
                    },
                    ButtonParams(text = stringResource(id = R.string.cancel)) {
                        showWifiConfigTips.value = false
                    }
                ),
                onDismiss = {
                    showWifiConfigTips.value = false
                }
            )
        } else {
            // 不支持局域网OTA，只显示去设置按钮
            BottomSheetTitleDes2ButtonCompat(
                stringResource(id = R.string.libs_please_config_network),
                stringResource(id = R.string.libs_please_config_network_desc),
                visible = showWifiConfigTips.value,
                buttonConfig = ButtonConfig.TwoButton(
                    ButtonParams(text = stringResource(id = R.string.cancel)) {
                        // 用户点击取消，
                        showWifiConfigTips.value = false
                    },
                    ButtonParams(
                        text = stringResource(id = R.string.libs_goto_setting),
                        textColor = ColorBlack,
                        enableColors = listOf(Color3AE1D2, Color3FD4FF)
                    ) {
                        HexaRouter.Settinng.navigateToWlan(this)
                    }
                ),
                onDismiss = { showWifiConfigTips.value = false }
            )
        }
    }

    private fun usePhoneUpgrade() {
        val netType = NetWorkUtil.getNetType(requireActivity())
        if (netType != "WIFI") {
            // 当前使用的不是WIFI
            showAppDownload.value = true
        } else {
            usePhoneDownload()
        }
    }

    @Composable
    private fun AppDownloadTipsDialog() {
        BottomSheetTitleDes2ButtonCompat(
            stringResource(id = R.string.libs_app_download_title),
            stringResource(id = R.string.libs_app_download_desc),
            visible = showAppDownload.value,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.libs_cancel)) {
                    // 用户点击取消，
                    showAppDownload.value = false
                },
                ButtonParams(
                    text = stringResource(id = R.string.libs_app_goon_download),
                    textColor = ColorBlack,
                    enableColors = listOf(Color3AE1D2, Color3FD4FF)
                ) {
                    usePhoneDownload()
                }
            ),
            onDismiss = { showAppDownload.value = false }
        )
    }

    /**
     * 使用手机进行下载&提示升级不能退出
     */
    private fun usePhoneDownload() {
        viewModel.otaType.value = OTAType.NET
        showOTATips.value = true
    }

    @Composable
    private fun LowStorageTipsDialog(otaStateState: State<MiWearOTAState>) {
        BottomSheetTitleDesOneButton(
            stringResource(id = R.string.libs_low_storage),
            stringResource(id = R.string.libs_low_storage_desc),
            visible = otaStateState.value.showLowStorage,
            buttonConfig =
            ButtonConfig.OneButton(
                ButtonParams(text = stringResource(id = R.string.libs_ota_known)) {}
            ),
            onDismiss = { viewModel.sendEvent(MiWearOTAEvent.SwitchLowStorageState(false)) }
        )
    }

    @Composable
    private fun DeviceNetTipsDialog(otaStateState: State<MiWearOTAState>) {
        val supportLanOTA = isSupportLanOTA()

        if (supportLanOTA) {
            // 支持局域网OTA，显示双按钮对话框
            BottomSheetTitleDes3Button(
                title = stringResource(id = R.string.libs_glasses_update_fail),
                des = stringResource(id = R.string.libs_glasses_update_net_error_des),
                visible = otaStateState.value.showDeviceNetErrorTips,
                buttonConfig = ButtonConfig.ThreeButton(
                    ButtonParams(
                        text = stringResource(R.string.libs_switch_net),
                        textColor = ColorBlack,
                        enableColors = listOf(Color3AE1D2, Color3FD4FF)
                    ) {
                        HexaRouter.Settinng.navigateToWlan(this)
                    },
                    ButtonParams(text = stringResource(id = R.string.libs_ota_through_phone)) {
                        // 用户点击通过手机升级
                        usePhoneUpgrade()
                    },
                    ButtonParams(text = stringResource(id = R.string.cancel)) {
                        viewModel.sendEvent(MiWearOTAEvent.SwitchDeviceNetErrorState(false))
                    }
                ),
                onDismiss = {
                    viewModel.sendEvent(MiWearOTAEvent.SwitchDeviceNetErrorState(false))
                }
            )
        } else {
            // 不支持局域网OTA，只显示切换网络按钮
            BottomSheetTitleDes2ButtonCompat(
                stringResource(id = R.string.libs_glasses_update_fail),
                stringResource(id = R.string.libs_glasses_update_net_error_des),
                visible = otaStateState.value.showDeviceNetErrorTips,
                buttonConfig = ButtonConfig.TwoButton(
                    ButtonParams(text = stringResource(id = R.string.cancel)) {
                        viewModel.sendEvent(MiWearOTAEvent.SwitchDeviceNetErrorState(false))
                    },
                    ButtonParams(
                        text = stringResource(id = R.string.libs_switch_net),
                        textColor = ColorBlack,
                        enableColors = listOf(Color3AE1D2, Color3FD4FF)
                    ) {
                        HexaRouter.Settinng.navigateToWlan(this)
                    }
                ),
                onDismiss = { viewModel.sendEvent(MiWearOTAEvent.SwitchDeviceNetErrorState(false)) }
            )
        }
    }

    @Composable
    private fun DeviceBusyTipsDialog(otaStateState: State<MiWearOTAState>) {
        BottomSheetTitleDesOneButton(
            stringResource(id = R.string.libs_device_basy),
            stringResource(id = R.string.libs_device_basy_desc),
            visible = otaStateState.value.showDeviceBusyTips,
            buttonConfig = ButtonConfig.OneButton(
                ButtonParams(text = stringResource(id = R.string.libs_ota_known)) { }
            ),
            onDismiss = { viewModel.sendEvent(MiWearOTAEvent.SwitchDeviceBusyState(false)) }
        )
    }

    @Composable
    private fun OTATipsDialog() {
        BottomSheetTitleDes2Button(
            stringResource(id = R.string.libs_ota_notices),
            stringResource(id = R.string.libs_ota_notices_desc),
            visible = showOTATips.value,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.libs_cancel)) {
                    O95Statistic.clickAlertCollectionEvent("ota_tip_cancel_button")
                },
                ButtonParams(
                    text = stringResource(id = R.string.libs_start_update),
                    textColor = ColorBlack,
                    enableColors = listOf(Color3AE1D2, Color3FD4FF)
                ) {
                    // 此处可能是使用app的方式进行下载亦或是直接让固件wifi升级
                    viewModel.sendEvent(MiWearOTAEvent.StartOta)
                    O95Statistic.clickAlertCollectionEvent("ota_tip_update_button")
                }
            ),
            onDismiss = { showOTATips.value = false }
        )
        O95Statistic.exposeTip43028("Alert_OTA_Tip")
    }

    @Composable
    private fun OTALowBatteryDialog(otaStateState: State<MiWearOTAState>) {
        BottomSheetTitleDesOneButton(
            stringResource(id = R.string.libs_glasses_low_battery),
            otaStateState.value.minBatteryTip,
            visible = otaStateState.value.showLowBattery,
            buttonConfig = ButtonConfig.OneButton(
                ButtonParams(text = stringResource(id = R.string.libs_ota_known)) {
                    O95Statistic.clickAlertCollectionEvent("ota_low_battery_ok_button")
                }
            ),
            onDismiss = { viewModel.sendEvent(MiWearOTAEvent.SwitchLowBatteryState(false)) }
        )
        O95Statistic.exposeTip43028("Alert_OTA_Low_Battery")
    }

    @Composable
    private fun OTALowTemperatureDialog(otaState: State<MiWearOTAState>) {
        BottomSheetTitleDesOneButton(
            stringResource(id = R.string.libs_glasses_low_temperature),
            des = otaState.value.temperatureTip,
            visible = otaState.value.showTemperatureDlg,
            buttonConfig = ButtonConfig.OneButton(
                ButtonParams(text = stringResource(id = R.string.libs_ota_known)) {
                    O95Statistic.clickAlertCollectionEvent("ota_temperature_ok_button")
                }
            ),
            onDismiss = { viewModel.sendEvent(MiWearOTAEvent.SwitchTemperatureState(false)) }
        )
        O95Statistic.exposeTip43028("Alert_OTA_Temperature")
    }

    @Composable
    private fun OTAUpgradeFailDialog(otaState: State<MiWearOTAState>) {
        BottomSheetTitleDesOneButton(
            title = otaState.value.upgradeFailTitle,
            des = otaState.value.upgradeFailTip,
            visible = otaState.value.showUpgradeFail,
            buttonConfig = ButtonConfig.OneButton(
                ButtonParams(text = stringResource(id = R.string.libs_ota_known)) {
                    O95Statistic.clickAlertCollectionEvent("ota_download_fail_ok_button")
                }
            ),
            onDismiss = { viewModel.sendEvent(MiWearOTAEvent.SwitchUpgradeFailState(false)) }
        )
    }

    @Composable
    fun WiFiDialog() {
        BottomSheetTitleDes2Button(
            stringResource(id = R.string.tip_request_enable_wifi),
            stringResource(id = R.string.libs_ota_enable_wifi_des),
            visible = showWiFiDlg.value,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.libs_cancel)) {
                    showWiFiDlg.value = false
                },
                ButtonParams(
                    textColor = ColorBlack,
                    text = stringResource(id = R.string.goOpen),
                    enableColors = listOf(Color3AE1D2, Color3FD4FF)
                ) {
                    showWiFiDlg.value = false
                    openSettings(action = Settings.ACTION_WIFI_SETTINGS)
                }
            )
        )
    }

    @Composable
    private fun RecordingDialog(interrupt: Boolean = false) {
        BottomSheetTitleDesOneButton(
            stringResource(
                id = if (interrupt) {
                    R.string.libs_device_trans_interrupt
                } else {
                    R.string.libs_device_can_not_trans
                }
            ),
            stringResource(
                id = if (interrupt) {
                    R.string.libs_device_trans_interrupt_desc
                } else {
                    R.string.libs_device_can_not_trans_desc
                }
            ),
            visible = showRecordingDlg.value,
            buttonConfig = ButtonConfig.OneButton(
                ButtonParams(text = stringResource(id = R.string.libs_known), onClick = {
                    showRecordingDlg.value = false
                })
            )
        )
        O95Statistic.exposeTip43028("Alert_Device_Recording")
    }

    private fun startP2PConnect(updateState: DeviceOTAState.Downloaded) {
        viewModel.startConnectP2P(this@MiWearOTAFragment) { state ->
            Timber.d("startP2PConnect called state:$state")
            when (state) {
                is FileTransO95Callback.WillTans -> {
                    viewModel.startUpload(requireContext(), updateState.filePath, updateState.md5)
                }

                is FileTransO95Callback.ConnectFailed -> {
                    viewModel.sendEvent(MiWearOTAEvent.SwitchUpgradeFailState(true))
                }

                is FileTransO95Callback.Recording -> {
                    showRecordingDlg.value = true
                }

                is FileTransO95Callback.LowBattery -> {
                    viewModel.sendEvent(MiWearOTAEvent.SwitchLowBatteryState(true))
                }

                is FileTransO95Callback.HighTemperature -> {
                    viewModel.sendEvent(MiWearOTAEvent.SwitchTemperatureState(true))
                }

                else -> {
                }
            }
        }
    }

    private fun openSettings(action: String) {
        val settingsIntent = Intent(action).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        startActivity(settingsIntent)
    }
}
